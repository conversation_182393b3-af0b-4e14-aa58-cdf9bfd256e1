name: 功能请求
description: 提出一个新功能的建议，帮助改进 subs-check
title: "[FEATURE] "
labels: ["enhancement"]
body:
  - type: checkboxes
    id: prerequisites
    attributes:
      label: 前置确认
      description: 请在提交前确认以下事项，避免重复或无效请求。
      options:
        - label: 我已阅读 [README.md](https://github.com/beck-8/subs-check/blob/master/README.md)，确认该功能尚未实现。
          required: true
        - label: 我已在 [Issues](https://github.com/beck-8/subs-check/issues) 中搜索，未找到类似的功能请求。
          required: true
        - label: 我已在 [config.example.yaml](https://github.com/beck-8/subs-check/blob/master/config/config.example.yaml) 中看过，未找到相关配置
          required: true
  - type: textarea
    id: description
    attributes:
      label: 功能描述
      description: 请详细描述你想要的功能（例如：支持新的订阅格式、改进测速逻辑等）。
      placeholder: "例如：我想添加对 v2ray 订阅的支持，以便..."
    validations:
      required: true
  - type: textarea
    id: use-case
    attributes:
      label: 使用场景
      description: 说明这个功能能解决什么问题或带来什么好处。
      placeholder: "例如：这能帮助我更高效地筛选可用节点。"
    validations:
      required: true
  - type: textarea
    id: additional-info
    attributes:
      label: 附加信息
      description: 可选：提供可能的实现思路、参考链接或其他相关资料。
      placeholder: "例如：可以参考某个工具的实现方式..."
    validations:
      required: false