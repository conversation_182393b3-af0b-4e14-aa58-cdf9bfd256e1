name: BUG 报告
description: 报告一个问题，帮助我们修复 subs-check
title: "[BUG] "
labels: ["bug"]
body:
  - type: checkboxes
    id: prerequisites
    attributes:
      label: 前置确认
      description: 请在提交前确认以下事项，避免重复或无效报告。
      options:
        - label: 我已阅读 [README.md](https://github.com/beck-8/subs-check/blob/master/README.md)，确认问题不是配置错误。
          required: true
        - label: 我已在 [Issues](https://github.com/beck-8/subs-check/issues) 中搜索，未找到类似的问题。
          required: true
        - label: 我已在 [config.example.yaml](https://github.com/beck-8/subs-check/blob/master/config/config.example.yaml) 中看过，未找到相关配置
          required: true
  - type: textarea
    id: description
    attributes:
      label: 问题描述
      description: 请简要描述你遇到的问题。
      placeholder: "例如：运行时出现崩溃，节点测试失败。"
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: 复现步骤
      description: 请列出复现问题的具体步骤。
      placeholder: "1. 配置了某个订阅\n2. 运行 docker 容器\n3. 出现错误"
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: 预期行为
      description: 描述你期望发生的事情。
      placeholder: "例如：应该成功导出 clash.meta 订阅。"
    validations:
      required: true
  - type: textarea
    id: actual
    attributes:
      label: 实际行为
      description: 描述实际发生的事情。
      placeholder: "例如：程序报错并退出。"
    validations:
      required: true
  - type: textarea
    id: subs-check-version
    attributes:
      label: subs-check 版本
      description: 提供 subs-check 版本。
      placeholder: "例如：v1.0.0或日志中当前版本字段"
    validations:
      required: true
  - type: textarea
    id: env-details
    attributes:
      label: 环境详情
      description: 提供系统版本、系统架构、Docker 版本等信息。
      placeholder: "例如：Ubuntu 20.04, x86_64, Docker v24.0.5"
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: 日志或截图
      description: 可选：提供错误日志、终端输出或截图。
      placeholder: "请粘贴相关内容（移除敏感信息）"
    validations:
      required: true