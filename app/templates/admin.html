<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subs Check 配置管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/editor/editor.main.css" rel="stylesheet">
    <style>
        body { padding: 15px; }
        
        /* 编辑器样式 - 使用动态高度计算 */
        #editor { 
            height: calc(100vh - 335px); /* 调整高度使其等于日志容器+进度条区域的总高度 */
            min-height: 300px;
            border: 1px solid #ddd;
            margin-bottom: 0;
        }
        
        /* 日志容器样式 - 使用动态高度计算 */
        .logs-container {
            height: calc(100vh - 400px);
            min-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            border: 1px solid #ddd;
            margin-bottom: 0;
        }
        
        /* 响应式调整 */
        @media (max-width: 991px) {
            body { padding: 10px; }
            #editor { height: calc(100vh - 285px); } /* 调整编辑器高度 */
            .logs-container { height: calc(100vh - 350px); }
            .card-header { padding: 0.5rem 0.75rem; }
            .card-body { padding: 0.75rem; }
            .card { margin-bottom: 15px; }
        }
        
        @media (max-width: 768px) {
            body { padding: 8px; }
            #editor { height: 420px; } /* 调整编辑器高度 */
            .logs-container { height: 350px; }
            .status-item { margin-bottom: 8px; }
            .config-path {
                max-width: 100%;
                white-space: normal;
                word-break: break-all;
                font-size: 0.85em;
            }
            .card { margin-bottom: 12px; }
            .progress-section { padding: 10px; }
        }
        
        /* 大屏幕优化 */
        @media (min-width: 1600px) {
            #editor { height: calc(100vh - 385px); } /* 调整编辑器高度 */
            .logs-container { height: calc(100vh - 450px); }
        }
        
        .card { margin-bottom: 15px; }
        
        /* 日志颜色样式 */
        .log-info { color: #28a745; }    /* 绿色 */
        .log-error { color: #dc3545; }   /* 红色 */
        .log-warn { color: #ffc107; }    /* 黄色 */
        .log-debug { color: #17a2b8; }   /* 青色 */
        .log-time { color: #6c757d; }    /* 灰色 */
        
        .status-bar {
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .config-path {
            white-space: normal;
            word-break: break-all;
        }
        
        .api-key-form {
            margin-bottom: 15px;
        }
        
        .btn {
            padding: 5px 10px;
            font-size: 0.875rem;
        }
        
        .btn-sm {
            padding: 3px 7px;
            font-size: 0.75rem;
        }
        
        .alert {
            padding: 0.75rem 1rem;
            margin-bottom: 15px;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .alert-info {
            background-color: #e8f4fd;
            color: #0c5460;
        }
        
        .alert-info a {
            color: #0c5460;
            text-decoration: underline;
        }
        
        .form-control {
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        
        h1 {
            color: #333;
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        /* 旋转动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .rotate-animation {
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        /* 配置编辑器蒙版 */
        .editor-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(235, 235, 235, 0.85);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }
        
        .editor-overlay-message {
            background-color: rgba(255, 255, 255, 0.5);
            color: #333;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            text-align: center;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }
        
        .editor-wrapper {
            position: relative;
        }
        
        /* 进度条样式 */
        .progress-section {
            padding: 12px;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .progress {
            height: 15px;
            position: relative;
        }
        
        .progress-bar {
            transition: width 0.5s ease;
        }
        
        .progress-bar-success {
            background-color: #28a745; /* 成功的进度条使用绿色 */
            position: absolute;
            height: 100%;
            left: 0;
            top: 0;
            z-index: 10;
            box-shadow: 0 0 8px #28a745; /* 添加发光效果 */
            border-radius: 0 4px 4px 0; /* 右侧圆角 */
        }
        
        .progress-bar-total {
            background-color: rgba(23, 162, 184, 0.5); /* 总进度条使用半透明青色 */
            position: absolute;
            height: 100%;
            left: 0;
            top: 0;
            z-index: 5;
        }
        
        /* 闪烁效果 */
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
        
        .success-highlight {
            animation: pulse 1.5s infinite;
            font-weight: bold;
            color: #28a745;
            padding: 0 2px;
            border-radius: 3px;
            background-color: rgba(40, 167, 69, 0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row align-items-center mb-3">
            <div class="col-md-8 col-sm-12">
                <h1 class="mb-0">Subs Check 配置管理</h1>
            </div>
            <div class="col-md-4 col-sm-12 text-md-end">
                <small class="text-muted" id="versionInfo">版本: 加载中...</small>
            </div>
        </div>
        
        <!-- 项目信息 -->
        <div class="alert alert-info mb-3 d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-github me-2"></i>项目地址: 
                <a href="https://github.com/beck-8/subs-check" target="_blank" class="text-break">https://github.com/beck-8/subs-check</a>
            </div>
            <div class="d-none d-md-block">
                <small>订阅检测工具</small>
            </div>
        </div>

        <!-- API密钥输入表单 -->
        <div class="card api-key-form">
            <div class="card-body py-2">
                <div class="row align-items-center">
                    <div class="col-md-3 col-sm-12 mb-sm-2">
                        <label for="apiKey" class="form-label mb-0"><strong>API密钥:</strong></label>
                    </div>
                    <div class="col-md-7 col-sm-8">
                        <div class="input-group">
                            <input type="password" class="form-control" id="apiKey" placeholder="请输入API密钥">
                            <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                <i class="bi bi-eye-slash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4 text-end mt-sm-2">
                        <button id="saveApiKey" class="btn btn-primary btn-sm">保存密钥</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="row">
                <div class="col-md-5 col-sm-12 status-item">
                    <strong>配置文件:</strong> 
                    <span class="config-path" title="{{.configPath}}">{{.configPath}}</span>
                </div>
                <div class="col-md-3 col-sm-6 status-item">
                    <strong>当前状态:</strong> 
                    <span id="statusContainer">
                        <span id="statusIcon" class="bi me-1"></span>
                        <span id="nextCheckTime">加载中...</span>
                    </span>
                </div>
                <div class="col-md-4 col-sm-6 status-item text-md-end">
                    <button id="forceClose" class="btn btn-danger btn-sm me-2">
                        <i class="bi bi-power me-1"></i>强制关闭
                    </button>
                    <button id="triggerCheck" class="btn btn-primary btn-sm">
                        <i class="bi bi-play-fill me-1"></i>立即检测
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧日志区域 -->
            <div class="col-lg-5 col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>最近日志</span>
                            <button id="refreshLogs" class="btn btn-secondary btn-sm">
                                <i class="bi bi-arrow-repeat me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="logs" class="logs-container"></div>
                    </div>
                    <!-- 进度条区域 -->
                    <div id="progressSection" class="progress-section">
                        <div class="progress-info" style="display: none;">
                            <div>处理进度: <span id="progressText">N/A</span></div>
                            <div><span id="progressPercent">N/A</span></div>
                        </div>
                        <div class="progress-info">
                            <div>成功节点: <span id="successText">N/A</span></div>
                            <div><span id="processPercent">N/A</span></div>
                        </div>
                        <div class="progress">
                            <div id="progressBarSuccess" class="progress-bar progress-bar-success" role="progressbar" style="width: 0%"></div>
                            <div id="progressBarTotal" class="progress-bar progress-bar-total" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧配置编辑器 -->
            <div class="col-lg-7 col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>配置编辑器</span>
                            <div>
                                <button id="toggleConfig" class="btn btn-outline-secondary btn-sm me-2" title="显示/隐藏配置">
                                    <i class="bi bi-eye-slash"></i>
                                </button>
                                <button id="saveConfig" class="btn btn-success btn-sm">
                                    <i class="bi bi-save me-1"></i>保存
                                </button>
                                <button id="reloadConfig" class="btn btn-secondary btn-sm ms-2">
                                    <i class="bi bi-arrow-repeat me-1"></i>重载
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="editor-wrapper">
                            <div id="editor"></div>
                            <div id="editorOverlay" class="editor-overlay">
                                <div class="editor-overlay-message">
                                    <i class="bi bi-eye-slash me-2"></i>配置已隐藏，点击查看
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Monaco Editor -->
    <script>var require = { paths: { 'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs' } };</script>
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/loader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/editor/editor.main.nls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/editor/editor.main.js"></script>
    
    <script>
        let editor;
        
        // 初始化API密钥
        const storedApiKey = localStorage.getItem('apiKey') || '';
        document.getElementById('apiKey').value = storedApiKey;
        
        // 切换API密钥可见性
        document.getElementById('toggleApiKey').addEventListener('click', function() {
            const apiKeyInput = document.getElementById('apiKey');
            const icon = this.querySelector('i');
            
            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                icon.className = 'bi bi-eye';
            } else {
                apiKeyInput.type = 'password';
                icon.className = 'bi bi-eye-slash';
            }
        });
        
        // 保存API密钥到本地存储
        document.getElementById('saveApiKey').addEventListener('click', function() {
            const apiKey = document.getElementById('apiKey').value.trim();
            localStorage.setItem('apiKey', apiKey);
            
            // 验证API密钥
            const button = this;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '验证中...';
            
            fetch('/api/status', {
                headers: { 'X-API-Key': apiKey }
            })
            .then(response => {
                if (response.status === 401) {
                    alert('API密钥无效，请检查后重试');
                    return false;
                } else if (response.ok) {
                    alert('API密钥有效，已保存到本地');
                    // 重新加载数据
                    loadConfig();
                    loadLogs();
                    updateStatus();
                    return true;
                } else {
                    throw new Error('验证失败');
                }
            })
            .catch(error => {
                console.error('验证API密钥失败:', error);
                alert('验证API密钥时出错，但已保存到本地存储');
            })
            .finally(() => {
                button.disabled = false;
                button.textContent = originalText;
            });
        });
        
        // 获取API密钥
        function getApiKey() {
            return localStorage.getItem('apiKey') || '';
        }
        
        // 添加API密钥到请求头
        function addApiKeyHeader(headers = {}) {
            const apiKey = getApiKey();
            return {
                ...headers,
                'X-API-Key': apiKey
            };
        }
        
        // 初始化编辑器
        require(['vs/editor/editor.main'], function() {
            // 注册YAML语言
            monaco.languages.register({ id: 'yaml' });
            monaco.languages.setMonarchTokensProvider('yaml', {
                tokenizer: {
                    root: [
                        [/^[\s]*#.*$/, 'comment'],
                        [/^[\s]*[\w\-]+:/, 'keyword'],
                        [/^[\s]*-/, 'keyword'],
                        [/".*?"/, 'string'],
                        [/'.*?'/, 'string'],
                    ]
                }
            });
            
            // 创建编辑器
            editor = monaco.editor.create(document.getElementById('editor'), {
                language: 'yaml',
                theme: 'vs',
                automaticLayout: true,
                minimap: { enabled: false }
            });
            
            // 加载配置
            loadConfig();
            
            // 设置编辑器遮罩层
            const toggleConfigBtn = document.getElementById('toggleConfig');
            const editorOverlay = document.getElementById('editorOverlay');
            
            // 默认隐藏配置
            editorOverlay.style.display = 'flex';
            
            // 点击按钮切换显示/隐藏
            toggleConfigBtn.addEventListener('click', function() {
                const isHidden = editorOverlay.style.display === 'flex';
                editorOverlay.style.display = isHidden ? 'none' : 'flex';
                
                // 更新按钮图标
                const icon = toggleConfigBtn.querySelector('i');
                icon.className = isHidden ? 'bi bi-eye' : 'bi bi-eye-slash';
            });
            
            // 点击遮罩层也可以切换显示
            editorOverlay.addEventListener('click', function() {
                editorOverlay.style.display = 'none';
                const icon = toggleConfigBtn.querySelector('i');
                icon.className = 'bi bi-eye';
            });
        });
        
        // 处理未授权响应
        function handleUnauthorized(response, showAlert = true) {
            if (response.status === 401) {
                if (showAlert) {
                    alert('API密钥无效或未提供，请检查您的API密钥');
                }
                return true;
            }
            return false;
        }
        
        // 加载配置
        function loadConfig() {
            return fetch('/api/config', {
                headers: addApiKeyHeader()
            })
            .then(response => {
                if (handleUnauthorized(response, false)) {
                    return;
                }
                if (!response.ok) {
                    throw new Error('加载配置失败');
                }
                return response.json();
            })
            .then(data => {
                if (editor && data) {
                    editor.setValue(data.content);
                }
                return data;
            })
            .catch(error => {
                console.error('加载配置失败:', error);
            });
        }
        
        // 保存配置
        document.getElementById('saveConfig').addEventListener('click', function() {
            const content = editor.getValue();
            
            fetch('/api/config', {
                method: 'POST',
                headers: addApiKeyHeader({
                    'Content-Type': 'application/json'
                }),
                body: JSON.stringify({ content })
            })
            .then(response => {
                if (handleUnauthorized(response, true)) {
                    throw new Error('未授权');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    alert('保存失败: ' + data.error);
                } else {
                    alert(data.message);
                    updateStatus();
                }
            })
            .catch(error => {
                if (error.message !== '未授权') {
                    alert('保存失败: ' + error.message);
                }
            });
        });
        
        // 重新加载配置
        document.getElementById('reloadConfig').addEventListener('click', function() {
            const button = this;
            const icon = button.querySelector('i');
            
            // 添加旋转动画
            icon.classList.add('rotate-animation');
            button.disabled = true;
            
            // 加载配置
            loadConfig()
                .finally(() => {
                    // 延迟移除动画，以便用户能看到
                    setTimeout(() => {
                        icon.classList.remove('rotate-animation');
                        button.disabled = false;
                    }, 500);
                });
        });
        
        // 强制关闭
        document.getElementById('forceClose').addEventListener('click', function() {
            if (!confirm('确定要强制关闭程序吗？这将立即终止所有正在进行的任务。')) {
                return;
            }
            
            const button = this;
            const icon = button.querySelector('i');
            const originalIcon = icon.className;
            
            // 替换图标并添加旋转动画
            icon.className = 'bi bi-arrow-repeat me-1 rotate-animation';
            button.disabled = true;
            
            fetch('/api/force-close', {
                method: 'POST',
                headers: addApiKeyHeader()
            })
            .then(response => {
                if (handleUnauthorized(response, true)) {
                    throw new Error('未授权');
                }
                return response.json();
            })
            .then(data => {
                // 什么都不做，只是发送请求
            })
            .catch(error => {
                if (error.message !== '未授权') {
                    console.error('强制关闭失败:', error);
                    alert('强制关闭失败: ' + error.message);
                }
            })
            .finally(() => {
                // 还原按钮
                setTimeout(() => {
                    icon.className = originalIcon;
                    button.disabled = false;
                }, 500);
            });
        });
        
        // 立即检测
        document.getElementById('triggerCheck').addEventListener('click', function() {
            const button = this;
            const icon = button.querySelector('i');
            const originalIcon = icon.className;
            
            // 替换图标并添加旋转动画
            icon.className = 'bi bi-arrow-repeat me-1 rotate-animation';
            button.disabled = true;
            
            fetch('/api/trigger-check', {
                method: 'POST',
                headers: addApiKeyHeader()
            })
            .then(response => {
                if (handleUnauthorized(response, true)) {
                    throw new Error('未授权');
                }
                return response.json();
            })
            .then(data => {
                loadLogs();
            })
            .catch(error => {
                if (error.message !== '未授权') {
                    console.error('触发检测失败:', error);
                }
            })
            .finally(() => {
                // 还原按钮
                setTimeout(() => {
                    icon.className = originalIcon;
                    button.disabled = false;
                }, 500);
            });
        });
        
        // 加载日志
        function loadLogs() {
            return fetch('/api/logs', {
                headers: addApiKeyHeader()
            })
            .then(response => {
                if (handleUnauthorized(response, false)) {
                    throw new Error('未授权');
                }
                return response.json();
            })
            .then(data => {
                const logsContainer = document.getElementById('logs');
                if (data.error) {
                    logsContainer.textContent = '加载日志失败: ' + data.error;
                } else {
                    // 记录当前滚动位置
                    const isScrolledToBottom = logsContainer.scrollHeight - logsContainer.clientHeight <= logsContainer.scrollTop + 1;
                    
                    logsContainer.innerHTML = '';
                    data.logs.forEach(log => {
                        const logLine = document.createElement('div');
                        logLine.innerHTML = colorizeLog(log);
                        logsContainer.appendChild(logLine);
                    });
                    
                    // 只有当用户在底部时才自动滚动
                    if (isScrolledToBottom) {
                        logsContainer.scrollTop = logsContainer.scrollHeight;
                    }
                }
                return data;
            })
            .catch(error => {
                if (error.message !== '未授权') {
                    document.getElementById('logs').textContent = '加载日志失败: ' + error.message;
                }
            });
        }
        
        // 为日志添加颜色
        function colorizeLog(log) {
            // 匹配时间部分
            log = log.replace(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/, '<span class="log-time">$1</span>');
            
            // 匹配日志级别并添加颜色
            log = log.replace(/\bINF\b/, '<span class="log-info">INF</span>');
            log = log.replace(/\bERR\b/, '<span class="log-error">ERR</span>');
            log = log.replace(/\bWRN\b/, '<span class="log-warn">WRN</span>');
            log = log.replace(/\bDBG\b/, '<span class="log-debug">DBG</span>');
            
            return log;
        }
        
        // 刷新日志
        document.getElementById('refreshLogs').addEventListener('click', function() {
            const button = this;
            const icon = button.querySelector('i');
            
            // 添加旋转动画
            icon.classList.add('rotate-animation');
            button.disabled = true;
            
            // 加载日志
            loadLogs()
                .finally(() => {
                    // 延迟移除动画，以便用户能看到
                    setTimeout(() => {
                        icon.classList.remove('rotate-animation');
                        button.disabled = false;
                    }, 500);
                });
        });
        
        // 更新进度条
        function updateProgressBar(total, processed, available) {
            // 计算检测进度百分比，保留一位小数
            const percentTotal = total > 0 ? (processed / total * 100).toFixed(1) : 0;
            
            // 更新总进度文本
            const progressText = document.getElementById('progressText');
            progressText.textContent = `${processed}/${total}`;
            
            // 更新总进度百分比文本
            const progressPercent = document.getElementById('progressPercent');
            progressPercent.textContent = `${percentTotal}%`;
            
            // 同时更新显示的处理进度百分比
            const processPercent = document.getElementById('processPercent');
            processPercent.textContent = `${percentTotal}%`;
            
            // 更新总进度条
            const progressBarTotal = document.getElementById('progressBarTotal');
            progressBarTotal.style.width = `${percentTotal}%`;
            progressBarTotal.setAttribute('aria-valuenow', percentTotal);
            
            // 计算成功率百分比，保留一位小数
            const percentSuccess = total > 0 ? (available / total * 100).toFixed(1) : 0;
            
            // 更新成功文本
            const successText = document.getElementById('successText');
            
            // 根据成功率决定是否添加高亮样式
            if (available > 0) { 
                // 任何时候有成功节点，都添加高亮效果
                successText.innerHTML = `<span class="success-highlight">${available}</span>/${total}`;
                
                // 更新成功进度条
                const progressBarSuccess = document.getElementById('progressBarSuccess');
                progressBarSuccess.style.width = `${percentSuccess}%`;
                progressBarSuccess.setAttribute('aria-valuenow', percentSuccess);
            } else {
                successText.textContent = `${available}/${total}`;
                
                // 更新成功进度条
                const progressBarSuccess = document.getElementById('progressBarSuccess');
                progressBarSuccess.style.width = `${percentSuccess}%`;
                progressBarSuccess.setAttribute('aria-valuenow', percentSuccess);
            }
        }
        
        // 更新状态
        function updateStatus() {
            fetch('/api/status', {
                headers: addApiKeyHeader()
            })
            .then(response => {
                if (handleUnauthorized(response, false)) {
                    return;
                }
                return response.json();
            })
            .then(data => {
                if (data) {
                    const statusContainer = document.getElementById('statusContainer');
                    const statusIcon = document.getElementById('statusIcon');
                    const statusText = document.getElementById('nextCheckTime');
                    
                    // 更新状态
                    if (data.checking) {
                        statusContainer.className = 'text-primary';
                        statusIcon.className = 'bi bi-arrow-repeat me-1 rotate-animation';
                        statusText.textContent = '正在检测中...';
                        
                        // 更新进度条 - 直接传递available参数
                        updateProgressBar(data.proxyCount, data.progress, data.available);
                    } else {
                        statusContainer.className = 'text-success';
                        statusIcon.className = 'bi bi-check-circle me-1';
                        statusText.textContent = '空闲';
                        
                        // 显示N/A
                        document.getElementById('progressText').textContent = 'N/A';
                        document.getElementById('progressPercent').textContent = 'N/A';
                        document.getElementById('processPercent').textContent = 'N/A';
                        document.getElementById('successText').textContent = 'N/A';
                        document.getElementById('progressBarTotal').style.width = '0%';
                        document.getElementById('progressBarSuccess').style.width = '0%';
                    }
                }
            })
            .catch(error => {
                console.error('获取状态失败:', error);
                const statusContainer = document.getElementById('statusContainer');
                const statusIcon = document.getElementById('statusIcon');
                const statusText = document.getElementById('nextCheckTime');
                
                statusContainer.className = 'text-danger';
                statusIcon.className = 'bi bi-exclamation-triangle me-1';
                statusText.textContent = '获取状态失败';
                
                // 显示N/A
                document.getElementById('progressText').textContent = 'N/A';
                document.getElementById('progressPercent').textContent = 'N/A';
                document.getElementById('processPercent').textContent = 'N/A';
                document.getElementById('successText').textContent = 'N/A';
                document.getElementById('progressBarTotal').style.width = '0%';
                document.getElementById('progressBarSuccess').style.width = '0%';
            });
        }
        
        // 初始加载
        loadConfig();
        updateStatus();
        loadLogs();
        
        // 检查API密钥
        const apiKey = localStorage.getItem('apiKey');
        if (!apiKey) {
            alert('请先输入API密钥以使用本系统');
            document.getElementById('apiKey').focus();
        }
        
        // 定时刷新
        setInterval(loadLogs, 10000);
        setInterval(updateStatus, 5000);
        
        // 获取版本信息
        function getVersionInfo() {
            fetch('/api/version', {
                headers: addApiKeyHeader()
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.version) {
                    document.getElementById('versionInfo').textContent = '版本: ' + data.version;
                }
            })
            .catch(error => {
                console.error('获取版本信息失败:', error);
                document.getElementById('versionInfo').textContent = '版本: 未知';
            });
        }
        
        // 初始加载版本信息
        getVersionInfo();
    </script>
    
    <!-- YAML解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/js-yaml@4.1.0/dist/js-yaml.min.js"></script>
</body>
</html>