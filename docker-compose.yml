
services:
  # subs-check 服务
  subs-check:
    build: .
    container_name: subs-check
    restart: always
    volumes:
      - ./config:/app/config
      - ./output:/app/output
    # 端口仅对本机暴露，外部流量由 cloudflared 隧道处理，更安全
    ports:
      - "127.0.0.1:8299:8299"
      - "127.0.0.1:8199:8199"
    environment:
      - TZ=Etc/UTC
      # API_KEY已迁移到config/config.yaml中统一管理

  # Cloudflare Tunnel 服务
  cloudflared:
    image: cloudflare/cloudflared:latest
    container_name: cloudflared-tunnel
    restart: always
    command: tunnel --no-autoupdate run --token eyJhIjoiYTZmOTNjZDdhNWNlMjRlZTJjMWU3ZTM1ZjFmYWM0MDUiLCJ0IjoiOWQ5NTZhMjctODBjZS00MDYyLWFhZWYtODM0MzMyY2EyYzljIiwicyI6Ik1UUXpaRFZtT0RVdFkyUmlOeTAwT1RJekxUZ3lOVE10T0RjMk5UUXhOamt5TVRnNCJ9
    depends_on:
      - subs-check # 确保 subs-check 服务先于 cloudflared 启动

  # Apprise 通知服务
  apprise:
    image: lscr.io/linuxserver/apprise-api:latest
    container_name: apprise-api
    restart: always
    ports:
      - "127.0.0.1:8000:8000" # 同样只对本机暴露
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Etc/UTC