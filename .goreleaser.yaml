before:
  hooks:
    - go mod tidy
release:
  prerelease: auto  # 自动识别 beta、rc 作为预发布

builds:
  - env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    goarch:
      - amd64
      - arm64
      - arm
      - "386"
    goarm:
      - "7"
    ignore:
      - goos: darwin
        goarch: arm
      - goos: windows
        goarch: arm
      - goos: darwin
        goarch: "386"
    mod_timestamp: '{{ .CommitTimestamp }}'
    flags:
      - -trimpath
    ldflags:
      - -s -w -X main.Version={{ .Version }} -X main.CurrentCommit={{ .ShortCommit }}

archives:
  - format: tar.gz
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else if eq .Arch "arm64" }}aarch64
      {{- else if eq .Arch "arm" }}armv7
      {{- else }}{{ .Arch }}{{ end }}
    format_overrides:
      - goos: windows
        format: zip

changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
      - '^chore:'
      - Merge pull request
      - Merge branch 