package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/beck-8/subs-check/check"
	"github.com/beck-8/subs-check/distributed"
	"github.com/beck-8/subs-check/distributed/transport"
	"github.com/beck-8/subs-check/save"
	"github.com/beck-8/subs-check/utils"
	"github.com/google/uuid"
)

// Collector 汇总服务器
type Collector struct {
	config      *distributed.DistributedConfig
	nodeInfo    *distributed.NodeInfo
	transport   transport.Transport
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	mu          sync.RWMutex
	
	// 节点管理
	agents      map[string]*distributed.NodeInfo
	lastSeen    map[string]time.Time
	
	// 任务管理
	tasks       map[string]*distributed.TaskInfo
	taskQueue   chan *distributed.TaskInfo
	
	// 数据聚合
	results     map[string][]*distributed.CheckResult // proxy_id -> results
	aggregator  *DataAggregator
	
	// 状态管理
	isRunning   bool
	startTime   time.Time
}

// NewCollector 创建新的汇总服务器
func NewCollector(cfg *distributed.DistributedConfig) (*Collector, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	nodeInfo := &distributed.NodeInfo{
		ID:       cfg.NodeID,
		Type:     distributed.NodeTypeCollector,
		Region:   cfg.Region,
		Location: cfg.Location,
		Version:  "1.0.0",
		LastSeen: time.Now(),
	}
	
	// 创建传输层
	transport, err := transport.NewTransport(cfg)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建传输层失败: %w", err)
	}
	
	collector := &Collector{
		config:    cfg,
		nodeInfo:  nodeInfo,
		transport: transport,
		ctx:       ctx,
		cancel:    cancel,
		agents:    make(map[string]*distributed.NodeInfo),
		lastSeen:  make(map[string]time.Time),
		tasks:     make(map[string]*distributed.TaskInfo),
		taskQueue: make(chan *distributed.TaskInfo, 1000),
		results:   make(map[string][]*distributed.CheckResult),
		aggregator: NewDataAggregator(cfg),
	}
	
	return collector, nil
}

// Start 启动汇总服务器
func (c *Collector) Start() error {
	c.mu.Lock()
	if c.isRunning {
		c.mu.Unlock()
		return fmt.Errorf("汇总服务器已在运行")
	}
	c.isRunning = true
	c.startTime = time.Now()
	c.mu.Unlock()
	
	slog.Info("启动汇总服务器", "node_id", c.nodeInfo.ID)
	
	// 启动传输层服务器
	if err := c.transport.Listen(c.ctx); err != nil {
		return fmt.Errorf("启动传输层失败: %w", err)
	}
	
	// 启动各个组件
	c.wg.Add(5)
	go c.messageHandler()
	go c.taskScheduler()
	go c.healthChecker()
	go c.dataProcessor()
	go c.statusReporter()
	
	slog.Info("汇总服务器启动成功", "node_id", c.nodeInfo.ID)
	return nil
}

// Stop 停止汇总服务器
func (c *Collector) Stop() error {
	c.mu.Lock()
	if !c.isRunning {
		c.mu.Unlock()
		return nil
	}
	c.isRunning = false
	c.mu.Unlock()
	
	slog.Info("停止汇总服务器", "node_id", c.nodeInfo.ID)
	
	// 停止所有组件
	c.cancel()
	c.wg.Wait()
	
	// 关闭传输层
	c.transport.Close()
	
	slog.Info("汇总服务器已停止", "node_id", c.nodeInfo.ID)
	return nil
}

// messageHandler 处理接收到的消息
func (c *Collector) messageHandler() {
	defer c.wg.Done()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			msg, err := c.transport.Receive(c.ctx)
			if err != nil {
				if c.ctx.Err() != nil {
					return
				}
				slog.Error("接收消息失败", "error", err)
				continue
			}
			
			if err := c.handleMessage(msg); err != nil {
				slog.Error("处理消息失败", "msg_type", msg.Type, "error", err)
			}
		}
	}
}

// handleMessage 处理单个消息
func (c *Collector) handleMessage(msg *distributed.Message) error {
	switch msg.Type {
	case distributed.MsgTypeNodeRegister:
		return c.handleNodeRegister(msg)
	case distributed.MsgTypeNodeUnregister:
		return c.handleNodeUnregister(msg)
	case distributed.MsgTypeNodeHeartbeat:
		return c.handleNodeHeartbeat(msg)
	case distributed.MsgTypeTaskProgress:
		return c.handleTaskProgress(msg)
	case distributed.MsgTypeDataReport:
		return c.handleDataReport(msg)
	default:
		slog.Debug("未知消息类型", "type", msg.Type)
		return nil
	}
}

// handleNodeRegister 处理节点注册
func (c *Collector) handleNodeRegister(msg *distributed.Message) error {
	nodeData, ok := msg.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("无效的节点数据格式")
	}
	
	nodeBytes, err := json.Marshal(nodeData)
	if err != nil {
		return fmt.Errorf("序列化节点数据失败: %w", err)
	}
	
	var nodeInfo distributed.NodeInfo
	if err := json.Unmarshal(nodeBytes, &nodeInfo); err != nil {
		return fmt.Errorf("反序列化节点信息失败: %w", err)
	}
	
	c.mu.Lock()
	c.agents[nodeInfo.ID] = &nodeInfo
	c.lastSeen[nodeInfo.ID] = time.Now()
	c.mu.Unlock()
	
	slog.Info("节点注册成功", "node_id", nodeInfo.ID, "region", nodeInfo.Region)
	return nil
}

// handleNodeUnregister 处理节点注销
func (c *Collector) handleNodeUnregister(msg *distributed.Message) error {
	c.mu.Lock()
	delete(c.agents, msg.From)
	delete(c.lastSeen, msg.From)
	c.mu.Unlock()
	
	slog.Info("节点注销", "node_id", msg.From)
	return nil
}

// handleNodeHeartbeat 处理节点心跳
func (c *Collector) handleNodeHeartbeat(msg *distributed.Message) error {
	c.mu.Lock()
	c.lastSeen[msg.From] = time.Now()
	c.mu.Unlock()
	
	return nil
}

// handleTaskProgress 处理任务进度
func (c *Collector) handleTaskProgress(msg *distributed.Message) error {
	progressData, ok := msg.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("无效的任务进度数据格式")
	}
	
	taskID, ok := progressData["task_id"].(string)
	if !ok {
		return fmt.Errorf("缺少任务ID")
	}
	
	status, ok := progressData["status"].(string)
	if !ok {
		return fmt.Errorf("缺少任务状态")
	}
	
	progress, _ := progressData["progress"].(float64)
	
	c.mu.Lock()
	if task, exists := c.tasks[taskID]; exists {
		task.Status = distributed.TaskStatus(status)
		task.Progress = progress
		
		if status == string(distributed.TaskStatusCompleted) {
			now := time.Now()
			task.CompletedAt = &now
		}
	}
	c.mu.Unlock()
	
	slog.Debug("任务进度更新", "task_id", taskID, "status", status, "progress", progress)
	return nil
}

// handleDataReport 处理数据上报
func (c *Collector) handleDataReport(msg *distributed.Message) error {
	resultData, ok := msg.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("无效的检测结果数据格式")
	}
	
	resultBytes, err := json.Marshal(resultData)
	if err != nil {
		return fmt.Errorf("序列化检测结果失败: %w", err)
	}
	
	var result distributed.CheckResult
	if err := json.Unmarshal(resultBytes, &result); err != nil {
		return fmt.Errorf("反序列化检测结果失败: %w", err)
	}
	
	// 添加到聚合器
	c.aggregator.AddResult(&result)
	
	slog.Debug("收到检测结果", "node_id", result.NodeID, "region", result.Region)
	return nil
}

// taskScheduler 任务调度器
func (c *Collector) taskScheduler() {
	defer c.wg.Done()
	
	ticker := time.NewTicker(c.config.CheckInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.scheduleCheckTasks()
		}
	}
}

// scheduleCheckTasks 调度检测任务
func (c *Collector) scheduleCheckTasks() {
	c.mu.RLock()
	agents := make([]*distributed.NodeInfo, 0, len(c.agents))
	for _, agent := range c.agents {
		agents = append(agents, agent)
	}
	c.mu.RUnlock()
	
	if len(agents) == 0 {
		slog.Debug("没有可用的检测节点")
		return
	}
	
	// 为每个节点创建检测任务
	for _, agent := range agents {
		task := &distributed.TaskInfo{
			ID:         uuid.New().String(),
			Type:       "proxy_check",
			Priority:   1,
			AssignedTo: agent.ID,
			CreatedAt:  time.Now(),
			Status:     distributed.TaskStatusPending,
			Config:     map[string]interface{}{},
			Progress:   0,
		}
		
		c.mu.Lock()
		c.tasks[task.ID] = task
		c.mu.Unlock()
		
		// 发送任务分配消息
		msg := &distributed.Message{
			ID:        uuid.New().String(),
			Type:      distributed.MsgTypeTaskAssign,
			From:      c.nodeInfo.ID,
			To:        agent.ID,
			Timestamp: time.Now(),
			Data:      task,
			Sequence:  0,
		}
		
		if err := c.transport.Send(msg); err != nil {
			slog.Error("发送任务分配失败", "agent_id", agent.ID, "error", err)
		} else {
			slog.Debug("任务分配成功", "task_id", task.ID, "agent_id", agent.ID)
		}
	}
}

// healthChecker 健康检查器
func (c *Collector) healthChecker() {
	defer c.wg.Done()
	
	ticker := time.NewTicker(c.config.HealthCheckTimeout)
	defer ticker.Stop()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.checkAgentHealth()
		}
	}
}

// checkAgentHealth 检查节点健康状态
func (c *Collector) checkAgentHealth() {
	now := time.Now()
	timeout := c.config.HealthCheckTimeout
	
	c.mu.Lock()
	for agentID, lastSeen := range c.lastSeen {
		if now.Sub(lastSeen) > timeout {
			slog.Warn("节点超时", "agent_id", agentID, "last_seen", lastSeen)
			delete(c.agents, agentID)
			delete(c.lastSeen, agentID)
		}
	}
	c.mu.Unlock()
}

// dataProcessor 数据处理器
func (c *Collector) dataProcessor() {
	defer c.wg.Done()
	
	ticker := time.NewTicker(c.config.AggregationWindow)
	defer ticker.Stop()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.processAggregatedData()
		}
	}
}

// processAggregatedData 处理聚合数据
func (c *Collector) processAggregatedData() {
	aggregatedResults := c.aggregator.GetAggregatedResults()
	
	if len(aggregatedResults) == 0 {
		slog.Debug("没有聚合结果需要处理")
		return
	}
	
	slog.Info("处理聚合数据", "results_count", len(aggregatedResults))
	
	// 转换为原有的检测结果格式
	var finalResults []check.Result
	for _, aggResult := range aggregatedResults {
		if len(aggResult.Results) > 0 {
			// 选择最佳结果
			bestResult := c.selectBestResult(aggResult)
			finalResults = append(finalResults, bestResult.Result)
		}
	}
	
	if len(finalResults) > 0 {
		// 保存结果
		save.SaveConfig(finalResults)
		
		// 发送通知
		c.sendNotification(finalResults)
		
		slog.Info("聚合数据处理完成", "final_results", len(finalResults))
	}
}

// selectBestResult 选择最佳检测结果
func (c *Collector) selectBestResult(aggResult *distributed.AggregatedResult) *distributed.CheckResult {
	if len(aggResult.Results) == 1 {
		return &aggResult.Results[0]
	}
	
	// 简单的选择策略：优先选择中国大陆的结果
	for _, result := range aggResult.Results {
		if result.Region == "CN" || result.Region == "China" {
			return &result
		}
	}
	
	// 如果没有中国大陆的结果，选择第一个
	return &aggResult.Results[0]
}

// sendNotification 发送通知
func (c *Collector) sendNotification(results []check.Result) {
	// 计算统计信息
	stats := utils.NotifyStats{
		AvailableNodes: len(results),
		StartTime:      time.Now(),
		Duration:       time.Since(c.startTime),
		TotalBytes:     0, // TODO: 从聚合结果中获取
	}
	
	// 计算流媒体统计
	for _, result := range results {
		if result.Netflix {
			stats.NetflixCount++
		}
		if result.Youtube != "" {
			stats.YouTubeCount++
		}
		if result.Openai {
			stats.OpenAICount++
		}
		if result.Disney {
			stats.DisneyCount++
		}
		if result.Gemini {
			stats.GeminiCount++
		}
		if result.TikTok != "" {
			stats.TikTokCount++
		}
	}
	
	// 发送增强通知
	utils.SendEnhancedNotify(stats)
}

// statusReporter 状态报告器
func (c *Collector) statusReporter() {
	defer c.wg.Done()
	
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.reportStatus()
		}
	}
}

// reportStatus 报告系统状态
func (c *Collector) reportStatus() {
	c.mu.RLock()
	agentCount := len(c.agents)
	taskCount := len(c.tasks)
	c.mu.RUnlock()
	
	slog.Info("系统状态报告",
		"agents", agentCount,
		"tasks", taskCount,
		"uptime", time.Since(c.startTime),
	)
}

// GetStatus 获取系统状态
func (c *Collector) GetStatus() *distributed.SystemStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	agents := make([]distributed.NodeInfo, 0, len(c.agents))
	for _, agent := range c.agents {
		agents = append(agents, *agent)
	}
	
	activeTasks := 0
	completedTasks := 0
	failedTasks := 0
	
	for _, task := range c.tasks {
		switch task.Status {
		case distributed.TaskStatusRunning, distributed.TaskStatusAssigned:
			activeTasks++
		case distributed.TaskStatusCompleted:
			completedTasks++
		case distributed.TaskStatusFailed:
			failedTasks++
		}
	}
	
	return &distributed.SystemStatus{
		CollectorInfo:  *c.nodeInfo,
		Agents:         agents,
		ActiveTasks:    activeTasks,
		CompletedTasks: completedTasks,
		FailedTasks:    failedTasks,
		Uptime:         time.Since(c.startTime),
	}
}
