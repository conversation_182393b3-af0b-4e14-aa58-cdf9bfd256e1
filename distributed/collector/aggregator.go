package collector

import (
	"fmt"
	"log/slog"
	"sort"
	"sync"
	"time"

	"github.com/beck-8/subs-check/distributed"
)

// DataAggregator 数据聚合器
type DataAggregator struct {
	config *distributed.DistributedConfig
	mu     sync.RWMutex
	
	// 原始检测结果存储
	rawResults map[string][]*distributed.CheckResult // proxy_url -> results
	
	// 聚合结果缓存
	aggregatedResults map[string]*distributed.AggregatedResult // proxy_url -> aggregated_result
	
	// 时间窗口管理
	windowStart time.Time
	windowEnd   time.Time
	
	// 统计信息
	totalResults    int
	processedResults int
	lastAggregation time.Time
}

// NewDataAggregator 创建数据聚合器
func NewDataAggregator(config *distributed.DistributedConfig) *DataAggregator {
	now := time.Now()
	return &DataAggregator{
		config:            config,
		rawResults:        make(map[string][]*distributed.CheckResult),
		aggregatedResults: make(map[string]*distributed.AggregatedResult),
		windowStart:       now,
		windowEnd:         now.Add(config.AggregationWindow),
	}
}

// AddResult 添加检测结果
func (da *DataAggregator) AddResult(result *distributed.CheckResult) {
	da.mu.Lock()
	defer da.mu.Unlock()
	
	// 检查时间窗口
	if result.Timestamp.After(da.windowEnd) {
		// 触发聚合并重置窗口
		da.aggregateResults()
		da.resetWindow()
	}
	
	// 获取代理标识
	proxyKey := da.getProxyKey(result)
	
	// 添加到原始结果
	da.rawResults[proxyKey] = append(da.rawResults[proxyKey], result)
	da.totalResults++
	
	slog.Debug("添加检测结果", 
		"proxy_key", proxyKey, 
		"node_id", result.NodeID, 
		"region", result.Region,
		"total_results", da.totalResults)
}

// getProxyKey 获取代理的唯一标识
func (da *DataAggregator) getProxyKey(result *distributed.CheckResult) string {
	// 从代理信息中提取关键字段作为标识
	if proxy, ok := result.Proxy["proxy"].(string); ok {
		return proxy
	}
	
	// 如果没有proxy字段，尝试其他字段
	if server, ok := result.Proxy["server"].(string); ok {
		if port, ok := result.Proxy["port"].(float64); ok {
			return fmt.Sprintf("%s:%d", server, int(port))
		}
		return server
	}
	
	// 最后使用IP作为标识
	return result.IP
}

// aggregateResults 聚合当前窗口的结果
func (da *DataAggregator) aggregateResults() {
	if len(da.rawResults) == 0 {
		return
	}
	
	slog.Info("开始聚合检测结果", 
		"proxy_count", len(da.rawResults),
		"total_results", da.totalResults,
		"window_start", da.windowStart,
		"window_end", da.windowEnd)
	
	// 清空之前的聚合结果
	da.aggregatedResults = make(map[string]*distributed.AggregatedResult)
	
	// 对每个代理进行聚合
	for proxyKey, results := range da.rawResults {
		if len(results) == 0 {
			continue
		}
		
		aggregated := da.aggregateProxyResults(proxyKey, results)
		if aggregated != nil {
			da.aggregatedResults[proxyKey] = aggregated
			da.processedResults += len(results)
		}
	}
	
	da.lastAggregation = time.Now()
	
	slog.Info("聚合完成", 
		"aggregated_proxies", len(da.aggregatedResults),
		"processed_results", da.processedResults)
}

// aggregateProxyResults 聚合单个代理的检测结果
func (da *DataAggregator) aggregateProxyResults(proxyKey string, results []*distributed.CheckResult) *distributed.AggregatedResult {
	if len(results) == 0 {
		return nil
	}
	
	// 按区域分组
	regionResults := make(map[string][]*distributed.CheckResult)
	regions := make([]string, 0)
	
	for _, result := range results {
		region := result.Region
		if region == "" {
			region = "Unknown"
		}
		
		if _, exists := regionResults[region]; !exists {
			regions = append(regions, region)
		}
		regionResults[region] = append(regionResults[region], result)
	}
	
	// 计算聚合统计
	aggregated := &distributed.AggregatedResult{
		ProxyInfo:    results[0].Proxy, // 使用第一个结果的代理信息
		Regions:      regions,
		Results:      make([]distributed.CheckResult, 0, len(results)),
		LastUpdated:  time.Now(),
		MediaSupport: make(map[string]bool),
	}
	
	// 选择每个区域的最佳结果
	var totalSpeed float64
	var speedCount int
	var successCount int
	
	for region, regionResultList := range regionResults {
		bestResult := da.selectBestResultForRegion(region, regionResultList)
		if bestResult != nil {
			aggregated.Results = append(aggregated.Results, *bestResult)
			
			// 累计统计信息
			if bestResult.Result.Openai || bestResult.Result.Netflix || bestResult.Result.Disney {
				successCount++
			}
			
			// 计算平均速度 (需要从检测结果中提取速度信息)
			if speed := da.extractSpeed(bestResult); speed > 0 {
				totalSpeed += speed
				speedCount++
			}
		}
	}
	
	// 计算成功率
	if len(aggregated.Results) > 0 {
		aggregated.SuccessRate = float64(successCount) / float64(len(aggregated.Results))
	}
	
	// 计算平均速度
	if speedCount > 0 {
		aggregated.AvgSpeed = totalSpeed / float64(speedCount)
	}
	
	// 聚合流媒体支持情况
	aggregated.MediaSupport = da.aggregateMediaSupport(aggregated.Results)
	
	// 选择最佳区域
	aggregated.BestRegion = da.selectBestRegion(aggregated.Results)
	
	// 计算置信度
	aggregated.Confidence = da.calculateConfidence(aggregated)
	
	return aggregated
}

// selectBestResultForRegion 为指定区域选择最佳检测结果
func (da *DataAggregator) selectBestResultForRegion(region string, results []*distributed.CheckResult) *distributed.CheckResult {
	if len(results) == 0 {
		return nil
	}
	
	if len(results) == 1 {
		return results[0]
	}
	
	// 按优先级排序：最新时间 > 更多流媒体支持 > 更好的网络质量
	sort.Slice(results, func(i, j int) bool {
		a, b := results[i], results[j]
		
		// 1. 优先选择最新的结果
		if !a.Timestamp.Equal(b.Timestamp) {
			return a.Timestamp.After(b.Timestamp)
		}
		
		// 2. 优先选择支持更多流媒体的结果
		scoreA := da.calculateMediaScore(a)
		scoreB := da.calculateMediaScore(b)
		if scoreA != scoreB {
			return scoreA > scoreB
		}
		
		// 3. 优先选择网络质量更好的结果
		return da.compareNetworkQuality(a, b)
	})
	
	return results[0]
}

// calculateMediaScore 计算流媒体支持分数
func (da *DataAggregator) calculateMediaScore(result *distributed.CheckResult) int {
	score := 0
	if result.Result.Openai {
		score += 10
	}
	if result.Result.Netflix {
		score += 8
	}
	if result.Result.Disney {
		score += 6
	}
	if result.Result.Gemini {
		score += 5
	}
	if result.Result.Youtube != "" && result.Result.Youtube != "No" {
		score += 4
	}
	if result.Result.TikTok != "" && result.Result.TikTok != "No" {
		score += 3
	}
	if result.Result.Google {
		score += 2
	}
	if result.Result.Cloudflare {
		score += 1
	}
	return score
}

// compareNetworkQuality 比较网络质量
func (da *DataAggregator) compareNetworkQuality(a, b *distributed.CheckResult) bool {
	// 优先选择风险较低的IP
	if a.IPRisk != b.IPRisk {
		return a.IPRisk < b.IPRisk
	}
	
	// 优先选择检测耗时较短的结果
	return a.Duration < b.Duration
}

// extractSpeed 从检测结果中提取速度信息
func (da *DataAggregator) extractSpeed(result *distributed.CheckResult) float64 {
	// 这里需要根据实际的检测结果结构来提取速度信息
	// 目前返回基于检测耗时的估算值
	if result.Duration > 0 {
		// 简单的速度估算：耗时越短，速度越快
		return 1000.0 / float64(result.Duration.Milliseconds())
	}
	return 0
}

// aggregateMediaSupport 聚合流媒体支持情况
func (da *DataAggregator) aggregateMediaSupport(results []distributed.CheckResult) map[string]bool {
	support := make(map[string]bool)
	
	// 统计各个流媒体的支持情况
	openaiCount := 0
	netflixCount := 0
	disneyCount := 0
	geminiCount := 0
	youtubeCount := 0
	tiktokCount := 0
	
	for _, result := range results {
		if result.Result.Openai {
			openaiCount++
		}
		if result.Result.Netflix {
			netflixCount++
		}
		if result.Result.Disney {
			disneyCount++
		}
		if result.Result.Gemini {
			geminiCount++
		}
		if result.Result.Youtube != "" && result.Result.Youtube != "No" {
			youtubeCount++
		}
		if result.Result.TikTok != "" && result.Result.TikTok != "No" {
			tiktokCount++
		}
	}
	
	// 如果超过一半的区域支持，则认为该代理支持该流媒体
	threshold := len(results) / 2
	support["openai"] = openaiCount > threshold
	support["netflix"] = netflixCount > threshold
	support["disney"] = disneyCount > threshold
	support["gemini"] = geminiCount > threshold
	support["youtube"] = youtubeCount > threshold
	support["tiktok"] = tiktokCount > threshold
	
	return support
}

// selectBestRegion 选择最佳区域
func (da *DataAggregator) selectBestRegion(results []distributed.CheckResult) string {
	if len(results) == 0 {
		return ""
	}
	
	// 优先选择中国大陆的结果
	for _, result := range results {
		if result.Region == "CN" || result.Region == "China" {
			return result.Region
		}
	}
	
	// 如果没有中国大陆的结果，选择流媒体支持最好的区域
	bestRegion := ""
	bestScore := -1
	
	for _, result := range results {
		score := da.calculateMediaScore(&result)
		if score > bestScore {
			bestScore = score
			bestRegion = result.Region
		}
	}
	
	return bestRegion
}

// calculateConfidence 计算置信度
func (da *DataAggregator) calculateConfidence(aggregated *distributed.AggregatedResult) float64 {
	if len(aggregated.Results) == 0 {
		return 0.0
	}
	
	confidence := 0.0
	
	// 基础置信度：基于区域数量
	regionCount := float64(len(aggregated.Results))
	if regionCount >= float64(da.config.MinRegions) {
		confidence += 0.4 // 40%基础置信度
	} else {
		confidence += 0.4 * (regionCount / float64(da.config.MinRegions))
	}
	
	// 成功率贡献
	confidence += aggregated.SuccessRate * 0.3 // 30%
	
	// 一致性贡献：检查各区域结果的一致性
	consistency := da.calculateConsistency(aggregated.Results)
	confidence += consistency * 0.3 // 30%
	
	// 确保置信度在0-1之间
	if confidence > 1.0 {
		confidence = 1.0
	}
	if confidence < 0.0 {
		confidence = 0.0
	}
	
	return confidence
}

// calculateConsistency 计算结果一致性
func (da *DataAggregator) calculateConsistency(results []distributed.CheckResult) float64 {
	if len(results) <= 1 {
		return 1.0
	}
	
	// 计算各个指标的一致性
	openaiConsistency := da.calculateBoolConsistency(results, func(r distributed.CheckResult) bool { return r.Result.Openai })
	netflixConsistency := da.calculateBoolConsistency(results, func(r distributed.CheckResult) bool { return r.Result.Netflix })
	disneyConsistency := da.calculateBoolConsistency(results, func(r distributed.CheckResult) bool { return r.Result.Disney })
	
	// 平均一致性
	return (openaiConsistency + netflixConsistency + disneyConsistency) / 3.0
}

// calculateBoolConsistency 计算布尔值的一致性
func (da *DataAggregator) calculateBoolConsistency(results []distributed.CheckResult, getter func(distributed.CheckResult) bool) float64 {
	if len(results) == 0 {
		return 0.0
	}
	
	trueCount := 0
	for _, result := range results {
		if getter(result) {
			trueCount++
		}
	}
	
	// 计算一致性：越接近全部一致，一致性越高
	ratio := float64(trueCount) / float64(len(results))
	if ratio > 0.5 {
		return ratio
	} else {
		return 1.0 - ratio
	}
}

// resetWindow 重置时间窗口
func (da *DataAggregator) resetWindow() {
	da.windowStart = time.Now()
	da.windowEnd = da.windowStart.Add(da.config.AggregationWindow)
	
	// 清空原始结果
	da.rawResults = make(map[string][]*distributed.CheckResult)
	da.totalResults = 0
}

// GetAggregatedResults 获取聚合结果
func (da *DataAggregator) GetAggregatedResults() []*distributed.AggregatedResult {
	da.mu.RLock()
	defer da.mu.RUnlock()
	
	// 如果有新的原始结果，先进行聚合
	if len(da.rawResults) > 0 && time.Since(da.lastAggregation) > time.Minute {
		da.mu.RUnlock()
		da.mu.Lock()
		da.aggregateResults()
		da.mu.Unlock()
		da.mu.RLock()
	}
	
	results := make([]*distributed.AggregatedResult, 0, len(da.aggregatedResults))
	for _, result := range da.aggregatedResults {
		// 只返回置信度达到阈值的结果
		if result.Confidence >= da.config.ConfidenceThreshold {
			results = append(results, result)
		}
	}
	
	// 按置信度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Confidence > results[j].Confidence
	})
	
	return results
}

// GetStatistics 获取聚合器统计信息
func (da *DataAggregator) GetStatistics() map[string]interface{} {
	da.mu.RLock()
	defer da.mu.RUnlock()
	
	return map[string]interface{}{
		"total_results":      da.totalResults,
		"processed_results":  da.processedResults,
		"aggregated_proxies": len(da.aggregatedResults),
		"window_start":       da.windowStart,
		"window_end":         da.windowEnd,
		"last_aggregation":   da.lastAggregation,
	}
}
