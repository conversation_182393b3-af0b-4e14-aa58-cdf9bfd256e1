package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/beck-8/subs-check/check"
	"github.com/beck-8/subs-check/distributed"
	"github.com/beck-8/subs-check/distributed/transport"
	"github.com/google/uuid"
)

// Agent 检测节点
type Agent struct {
	config     *distributed.DistributedConfig
	nodeInfo   *distributed.NodeInfo
	transport  transport.Transport
	taskQueue  chan *distributed.TaskInfo
	results    chan *distributed.CheckResult
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	mu         sync.RWMutex
	
	// 状态管理
	isRunning    bool
	activeTasks  map[string]*distributed.TaskInfo
	lastHeartbeat time.Time
}

// NewAgent 创建新的检测节点
func NewAgent(cfg *distributed.DistributedConfig) (*Agent, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	nodeInfo := &distributed.NodeInfo{
		ID:       cfg.NodeID,
		Type:     distributed.NodeTypeAgent,
		Region:   cfg.Region,
		Location: cfg.Location,
		Version:  "1.0.0", // TODO: 从构建信息获取
		LastSeen: time.Now(),
	}
	
	// 创建传输层
	transport, err := transport.NewTransport(cfg)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建传输层失败: %w", err)
	}
	
	agent := &Agent{
		config:      cfg,
		nodeInfo:    nodeInfo,
		transport:   transport,
		taskQueue:   make(chan *distributed.TaskInfo, 100),
		results:     make(chan *distributed.CheckResult, 100),
		ctx:         ctx,
		cancel:      cancel,
		activeTasks: make(map[string]*distributed.TaskInfo),
	}
	
	return agent, nil
}

// Start 启动检测节点
func (a *Agent) Start() error {
	a.mu.Lock()
	if a.isRunning {
		a.mu.Unlock()
		return fmt.Errorf("节点已在运行")
	}
	a.isRunning = true
	a.mu.Unlock()
	
	slog.Info("启动检测节点", "node_id", a.nodeInfo.ID, "region", a.nodeInfo.Region)
	
	// 连接到汇总服务器
	if err := a.transport.Connect(a.ctx); err != nil {
		return fmt.Errorf("连接汇总服务器失败: %w", err)
	}
	
	// 注册节点
	if err := a.registerNode(); err != nil {
		return fmt.Errorf("注册节点失败: %w", err)
	}
	
	// 启动各个组件
	a.wg.Add(4)
	go a.messageHandler()
	go a.taskProcessor()
	go a.resultSender()
	go a.heartbeatSender()
	
	slog.Info("检测节点启动成功", "node_id", a.nodeInfo.ID)
	return nil
}

// Stop 停止检测节点
func (a *Agent) Stop() error {
	a.mu.Lock()
	if !a.isRunning {
		a.mu.Unlock()
		return nil
	}
	a.isRunning = false
	a.mu.Unlock()
	
	slog.Info("停止检测节点", "node_id", a.nodeInfo.ID)
	
	// 注销节点
	a.unregisterNode()
	
	// 停止所有组件
	a.cancel()
	a.wg.Wait()
	
	// 关闭传输层
	a.transport.Close()
	
	slog.Info("检测节点已停止", "node_id", a.nodeInfo.ID)
	return nil
}

// registerNode 注册节点到汇总服务器
func (a *Agent) registerNode() error {
	msg := &distributed.Message{
		ID:        uuid.New().String(),
		Type:      distributed.MsgTypeNodeRegister,
		From:      a.nodeInfo.ID,
		To:        "", // 发送给汇总服务器
		Timestamp: time.Now(),
		Data:      a.nodeInfo,
		Sequence:  0,
	}
	
	return a.transport.Send(msg)
}

// unregisterNode 从汇总服务器注销节点
func (a *Agent) unregisterNode() error {
	msg := &distributed.Message{
		ID:        uuid.New().String(),
		Type:      distributed.MsgTypeNodeUnregister,
		From:      a.nodeInfo.ID,
		To:        "",
		Timestamp: time.Now(),
		Data:      a.nodeInfo,
		Sequence:  0,
	}
	
	return a.transport.Send(msg)
}

// messageHandler 处理接收到的消息
func (a *Agent) messageHandler() {
	defer a.wg.Done()
	
	for {
		select {
		case <-a.ctx.Done():
			return
		default:
			msg, err := a.transport.Receive(a.ctx)
			if err != nil {
				if a.ctx.Err() != nil {
					return
				}
				slog.Error("接收消息失败", "error", err)
				continue
			}
			
			if err := a.handleMessage(msg); err != nil {
				slog.Error("处理消息失败", "msg_type", msg.Type, "error", err)
			}
		}
	}
}

// handleMessage 处理单个消息
func (a *Agent) handleMessage(msg *distributed.Message) error {
	switch msg.Type {
	case distributed.MsgTypeTaskAssign:
		return a.handleTaskAssign(msg)
	case distributed.MsgTypeConfigUpdate:
		return a.handleConfigUpdate(msg)
	case distributed.MsgTypeSystemShutdown:
		return a.handleSystemShutdown(msg)
	default:
		slog.Debug("未知消息类型", "type", msg.Type)
		return nil
	}
}

// handleTaskAssign 处理任务分配
func (a *Agent) handleTaskAssign(msg *distributed.Message) error {
	taskData, ok := msg.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("无效的任务数据格式")
	}
	
	taskBytes, err := json.Marshal(taskData)
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %w", err)
	}
	
	var task distributed.TaskInfo
	if err := json.Unmarshal(taskBytes, &task); err != nil {
		return fmt.Errorf("反序列化任务失败: %w", err)
	}
	
	// 添加到任务队列
	select {
	case a.taskQueue <- &task:
		slog.Info("接收到新任务", "task_id", task.ID, "type", task.Type)
		return nil
	case <-a.ctx.Done():
		return a.ctx.Err()
	default:
		return fmt.Errorf("任务队列已满")
	}
}

// handleConfigUpdate 处理配置更新
func (a *Agent) handleConfigUpdate(msg *distributed.Message) error {
	// TODO: 实现配置更新逻辑
	slog.Info("收到配置更新", "from", msg.From)
	return nil
}

// handleSystemShutdown 处理系统关闭
func (a *Agent) handleSystemShutdown(msg *distributed.Message) error {
	slog.Info("收到系统关闭信号", "from", msg.From)
	go func() {
		time.Sleep(time.Second) // 给一点时间处理当前任务
		a.Stop()
	}()
	return nil
}

// taskProcessor 处理检测任务
func (a *Agent) taskProcessor() {
	defer a.wg.Done()
	
	for {
		select {
		case <-a.ctx.Done():
			return
		case task := <-a.taskQueue:
			a.processTask(task)
		}
	}
}

// processTask 处理单个检测任务
func (a *Agent) processTask(task *distributed.TaskInfo) {
	a.mu.Lock()
	a.activeTasks[task.ID] = task
	a.mu.Unlock()
	
	defer func() {
		a.mu.Lock()
		delete(a.activeTasks, task.ID)
		a.mu.Unlock()
	}()
	
	slog.Info("开始处理任务", "task_id", task.ID, "type", task.Type)
	
	// 发送任务开始消息
	a.sendTaskStatus(task.ID, distributed.TaskStatusRunning, 0)
	
	startTime := time.Now()
	
	// 执行检测
	results, err := a.executeCheck(task)
	if err != nil {
		slog.Error("执行检测失败", "task_id", task.ID, "error", err)
		a.sendTaskStatus(task.ID, distributed.TaskStatusFailed, 0)
		return
	}
	
	duration := time.Since(startTime)
	
	// 发送检测结果
	for _, result := range results {
		result.NodeID = a.nodeInfo.ID
		result.TaskID = task.ID
		result.Region = a.nodeInfo.Region
		result.Timestamp = time.Now()
		result.Duration = duration
		
		select {
		case a.results <- result:
		case <-a.ctx.Done():
			return
		}
	}
	
	// 发送任务完成消息
	a.sendTaskStatus(task.ID, distributed.TaskStatusCompleted, 100)
	
	slog.Info("任务处理完成", "task_id", task.ID, "results", len(results), "duration", duration)
}

// executeCheck 执行实际的检测逻辑
func (a *Agent) executeCheck(task *distributed.TaskInfo) ([]*distributed.CheckResult, error) {
	// 复用现有的检测逻辑
	results, err := check.Check()
	if err != nil {
		return nil, err
	}
	
	// 转换为分布式检测结果
	var distResults []*distributed.CheckResult
	for _, result := range results {
		distResult := &distributed.CheckResult{
			Result: result,
			// NetworkInfo 需要通过其他方式获取
			NetworkInfo: a.getNetworkInfo(),
		}
		distResults = append(distResults, distResult)
	}
	
	return distResults, nil
}

// getNetworkInfo 获取网络信息
func (a *Agent) getNetworkInfo() distributed.NetworkInfo {
	// TODO: 实现网络信息获取逻辑
	return distributed.NetworkInfo{
		ISP:     "Unknown",
		Country: a.nodeInfo.Region,
		City:    a.nodeInfo.Location,
	}
}

// sendTaskStatus 发送任务状态
func (a *Agent) sendTaskStatus(taskID string, status distributed.TaskStatus, progress float64) {
	msg := &distributed.Message{
		ID:        uuid.New().String(),
		Type:      distributed.MsgTypeTaskProgress,
		From:      a.nodeInfo.ID,
		To:        "",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"task_id":  taskID,
			"status":   status,
			"progress": progress,
		},
		Sequence: 0,
	}
	
	if err := a.transport.Send(msg); err != nil {
		slog.Error("发送任务状态失败", "task_id", taskID, "error", err)
	}
}

// resultSender 发送检测结果
func (a *Agent) resultSender() {
	defer a.wg.Done()
	
	for {
		select {
		case <-a.ctx.Done():
			return
		case result := <-a.results:
			msg := &distributed.Message{
				ID:        uuid.New().String(),
				Type:      distributed.MsgTypeDataReport,
				From:      a.nodeInfo.ID,
				To:        "",
				Timestamp: time.Now(),
				Data:      result,
				Sequence:  0,
			}
			
			if err := a.transport.Send(msg); err != nil {
				slog.Error("发送检测结果失败", "error", err)
			}
		}
	}
}

// heartbeatSender 发送心跳
func (a *Agent) heartbeatSender() {
	defer a.wg.Done()
	
	ticker := time.NewTicker(a.config.HeartbeatInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-a.ctx.Done():
			return
		case <-ticker.C:
			a.sendHeartbeat()
		}
	}
}

// sendHeartbeat 发送心跳消息
func (a *Agent) sendHeartbeat() {
	a.nodeInfo.LastSeen = time.Now()
	a.lastHeartbeat = time.Now()
	
	msg := &distributed.Message{
		ID:        uuid.New().String(),
		Type:      distributed.MsgTypeNodeHeartbeat,
		From:      a.nodeInfo.ID,
		To:        "",
		Timestamp: time.Now(),
		Data:      a.nodeInfo,
		Sequence:  0,
	}
	
	if err := a.transport.Send(msg); err != nil {
		slog.Error("发送心跳失败", "error", err)
	}
}

// GetStatus 获取节点状态
func (a *Agent) GetStatus() map[string]interface{} {
	a.mu.RLock()
	defer a.mu.RUnlock()
	
	return map[string]interface{}{
		"node_info":      a.nodeInfo,
		"is_running":     a.isRunning,
		"active_tasks":   len(a.activeTasks),
		"last_heartbeat": a.lastHeartbeat,
		"queue_size":     len(a.taskQueue),
		"results_pending": len(a.results),
	}
}
