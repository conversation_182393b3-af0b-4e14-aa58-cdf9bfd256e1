package transport

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/beck-8/subs-check/distributed"
	"github.com/gorilla/websocket"
)

// WebSocketTransport WebSocket传输实现
type WebSocketTransport struct {
	config      *distributed.DistributedConfig
	conn        *websocket.Conn
	server      *http.Server
	upgrader    websocket.Upgrader
	ctx         context.Context
	cancel      context.CancelFunc
	mu          sync.RWMutex
	isConnected bool
	isServer    bool
	
	// 消息通道
	incomingMsgs chan *distributed.Message
	outgoingMsgs chan *distributed.Message
	
	// 客户端连接管理 (服务器端使用)
	clients     map[string]*websocket.Conn
	clientsMu   sync.RWMutex
}

// NewWebSocketTransport 创建WebSocket传输实例
func NewWebSocketTransport(config *distributed.DistributedConfig) (*WebSocketTransport, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	transport := &WebSocketTransport{
		config:       config,
		ctx:          ctx,
		cancel:       cancel,
		incomingMsgs: make(chan *distributed.Message, 1000),
		outgoingMsgs: make(chan *distributed.Message, 1000),
		clients:      make(map[string]*websocket.Conn),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许所有来源 (生产环境应该更严格)
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}
	
	return transport, nil
}

// Connect 连接到WebSocket服务器 (客户端模式)
func (wt *WebSocketTransport) Connect(ctx context.Context) error {
	wt.mu.Lock()
	defer wt.mu.Unlock()
	
	if wt.isConnected {
		return nil
	}
	
	if wt.config.CollectorURL == "" {
		return fmt.Errorf("未配置汇总服务器URL")
	}
	
	// 解析URL
	u, err := url.Parse(wt.config.CollectorURL)
	if err != nil {
		return fmt.Errorf("解析服务器URL失败: %w", err)
	}
	
	// 构建WebSocket URL
	if u.Scheme == "http" {
		u.Scheme = "ws"
	} else if u.Scheme == "https" {
		u.Scheme = "wss"
	}
	
	if u.Path == "" {
		u.Path = "/ws"
	}
	
	// 添加节点ID作为查询参数
	query := u.Query()
	query.Set("node_id", wt.config.NodeID)
	query.Set("node_type", string(wt.config.NodeType))
	query.Set("region", wt.config.Region)
	u.RawQuery = query.Encode()
	
	// 配置拨号器
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 30 * time.Second
	
	// 如果启用TLS
	if wt.config.EnableTLS {
		dialer.TLSClientConfig = &tls.Config{
			InsecureSkipVerify: false, // 生产环境应该验证证书
		}
	}
	
	// 设置请求头
	headers := http.Header{}
	if wt.config.AuthToken != "" {
		headers.Set("Authorization", "Bearer "+wt.config.AuthToken)
	}
	
	// 连接到服务器
	slog.Info("连接WebSocket服务器", "url", u.String(), "node_id", wt.config.NodeID)
	
	wt.conn, _, err = dialer.DialContext(ctx, u.String(), headers)
	if err != nil {
		return fmt.Errorf("连接WebSocket服务器失败: %w", err)
	}
	
	wt.isConnected = true
	wt.isServer = false
	
	// 启动消息处理器
	go wt.messageHandler()
	go wt.messageSender()
	
	slog.Info("WebSocket客户端连接成功", "node_id", wt.config.NodeID)
	return nil
}

// Listen 启动WebSocket服务器 (服务器模式)
func (wt *WebSocketTransport) Listen(ctx context.Context) error {
	wt.mu.Lock()
	defer wt.mu.Unlock()
	
	if wt.isConnected {
		return nil
	}
	
	endpoint := wt.config.WSEndpoint
	if endpoint == "" {
		endpoint = ":8080"
	}
	
	// 创建HTTP服务器
	mux := http.NewServeMux()
	mux.HandleFunc("/ws", wt.handleWebSocket)
	mux.HandleFunc("/health", wt.handleHealth)
	
	wt.server = &http.Server{
		Addr:    endpoint,
		Handler: mux,
	}
	
	// 如果启用TLS
	if wt.config.EnableTLS && wt.config.CertFile != "" && wt.config.KeyFile != "" {
		wt.server.TLSConfig = &tls.Config{
			MinVersion: tls.VersionTLS12,
		}
	}
	
	wt.isConnected = true
	wt.isServer = true
	
	// 启动消息发送器
	go wt.messageSender()
	
	// 启动服务器
	go func() {
		var err error
		if wt.config.EnableTLS && wt.config.CertFile != "" && wt.config.KeyFile != "" {
			slog.Info("启动WebSocket TLS服务器", "endpoint", endpoint)
			err = wt.server.ListenAndServeTLS(wt.config.CertFile, wt.config.KeyFile)
		} else {
			slog.Info("启动WebSocket服务器", "endpoint", endpoint)
			err = wt.server.ListenAndServe()
		}
		
		if err != nil && err != http.ErrServerClosed {
			slog.Error("WebSocket服务器启动失败", "error", err)
		}
	}()
	
	slog.Info("WebSocket服务器启动成功", "endpoint", endpoint)
	return nil
}

// handleWebSocket 处理WebSocket连接
func (wt *WebSocketTransport) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	// 验证认证令牌
	if wt.config.AuthToken != "" {
		auth := r.Header.Get("Authorization")
		if auth != "Bearer "+wt.config.AuthToken {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
	}
	
	// 升级连接
	conn, err := wt.upgrader.Upgrade(w, r, nil)
	if err != nil {
		slog.Error("升级WebSocket连接失败", "error", err)
		return
	}
	
	// 获取节点信息
	nodeID := r.URL.Query().Get("node_id")
	nodeType := r.URL.Query().Get("node_type")
	region := r.URL.Query().Get("region")
	
	if nodeID == "" {
		slog.Error("缺少节点ID")
		conn.Close()
		return
	}
	
	slog.Info("新的WebSocket连接", "node_id", nodeID, "type", nodeType, "region", region)
	
	// 添加到客户端列表
	wt.clientsMu.Lock()
	wt.clients[nodeID] = conn
	wt.clientsMu.Unlock()
	
	// 处理客户端消息
	go wt.handleClient(nodeID, conn)
}

// handleClient 处理单个客户端连接
func (wt *WebSocketTransport) handleClient(nodeID string, conn *websocket.Conn) {
	defer func() {
		// 移除客户端
		wt.clientsMu.Lock()
		delete(wt.clients, nodeID)
		wt.clientsMu.Unlock()
		
		conn.Close()
		slog.Info("WebSocket客户端断开", "node_id", nodeID)
	}()
	
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})
	
	for {
		select {
		case <-wt.ctx.Done():
			return
		default:
			// 读取消息
			_, data, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					slog.Error("WebSocket读取错误", "node_id", nodeID, "error", err)
				}
				return
			}
			
			// 反序列化消息
			var msg distributed.Message
			if err := json.Unmarshal(data, &msg); err != nil {
				slog.Error("反序列化消息失败", "node_id", nodeID, "error", err)
				continue
			}
			
			// 发送到内部通道
			select {
			case wt.incomingMsgs <- &msg:
				slog.Debug("接收WebSocket消息", "msg_type", msg.Type, "from", msg.From)
			case <-wt.ctx.Done():
				return
			default:
				slog.Warn("消息通道已满，丢弃消息", "msg_type", msg.Type)
			}
		}
	}
}

// handleHealth 健康检查端点
func (wt *WebSocketTransport) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	
	wt.clientsMu.RLock()
	clientCount := len(wt.clients)
	wt.clientsMu.RUnlock()
	
	response := map[string]interface{}{
		"status":  "ok",
		"clients": clientCount,
		"uptime":  time.Since(time.Now()).String(), // TODO: 记录启动时间
	}
	
	json.NewEncoder(w).Encode(response)
}

// messageHandler 消息处理器 (客户端模式)
func (wt *WebSocketTransport) messageHandler() {
	defer close(wt.incomingMsgs)
	
	// 设置ping处理器
	wt.conn.SetPingHandler(func(appData string) error {
		return wt.conn.WriteControl(websocket.PongMessage, []byte(appData), time.Now().Add(time.Second))
	})
	
	for {
		select {
		case <-wt.ctx.Done():
			return
		default:
			// 读取消息
			_, data, err := wt.conn.ReadMessage()
			if err != nil {
				if wt.ctx.Err() != nil {
					return
				}
				slog.Error("WebSocket读取错误", "error", err)
				return
			}
			
			// 反序列化消息
			var msg distributed.Message
			if err := json.Unmarshal(data, &msg); err != nil {
				slog.Error("反序列化消息失败", "error", err)
				continue
			}
			
			// 发送到内部通道
			select {
			case wt.incomingMsgs <- &msg:
				slog.Debug("接收WebSocket消息", "msg_type", msg.Type, "from", msg.From)
			case <-wt.ctx.Done():
				return
			default:
				slog.Warn("消息通道已满，丢弃消息", "msg_type", msg.Type)
			}
		}
	}
}

// messageSender 消息发送器
func (wt *WebSocketTransport) messageSender() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-wt.ctx.Done():
			return
		case <-ticker.C:
			// 发送ping消息保持连接
			if !wt.isServer && wt.conn != nil {
				if err := wt.conn.WriteControl(websocket.PingMessage, nil, time.Now().Add(time.Second)); err != nil {
					slog.Error("发送ping失败", "error", err)
				}
			}
		case msg := <-wt.outgoingMsgs:
			if err := wt.sendMessage(msg); err != nil {
				slog.Error("发送消息失败", "error", err)
			}
		}
	}
}

// sendMessage 发送消息
func (wt *WebSocketTransport) sendMessage(msg *distributed.Message) error {
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}
	
	if wt.isServer {
		// 服务器模式：发送给指定客户端或广播
		if msg.To == "" {
			// 广播消息
			wt.clientsMu.RLock()
			for nodeID, conn := range wt.clients {
				if err := conn.WriteMessage(websocket.TextMessage, data); err != nil {
					slog.Error("发送广播消息失败", "node_id", nodeID, "error", err)
				}
			}
			wt.clientsMu.RUnlock()
		} else {
			// 点对点消息
			wt.clientsMu.RLock()
			conn, exists := wt.clients[msg.To]
			wt.clientsMu.RUnlock()
			
			if !exists {
				return fmt.Errorf("目标节点不在线: %s", msg.To)
			}
			
			if err := conn.WriteMessage(websocket.TextMessage, data); err != nil {
				return fmt.Errorf("发送消息失败: %w", err)
			}
		}
	} else {
		// 客户端模式：发送给服务器
		if err := wt.conn.WriteMessage(websocket.TextMessage, data); err != nil {
			return fmt.Errorf("发送消息失败: %w", err)
		}
	}
	
	slog.Debug("发送WebSocket消息", "msg_type", msg.Type, "to", msg.To)
	return nil
}

// Send 发送消息
func (wt *WebSocketTransport) Send(msg *distributed.Message) error {
	select {
	case wt.outgoingMsgs <- msg:
		return nil
	case <-wt.ctx.Done():
		return wt.ctx.Err()
	default:
		return fmt.Errorf("发送队列已满")
	}
}

// Receive 接收消息
func (wt *WebSocketTransport) Receive(ctx context.Context) (*distributed.Message, error) {
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case msg, ok := <-wt.incomingMsgs:
		if !ok {
			return nil, fmt.Errorf("消息通道已关闭")
		}
		return msg, nil
	}
}

// Close 关闭连接
func (wt *WebSocketTransport) Close() error {
	wt.mu.Lock()
	defer wt.mu.Unlock()
	
	if !wt.isConnected {
		return nil
	}
	
	wt.cancel()
	
	if wt.isServer {
		// 关闭所有客户端连接
		wt.clientsMu.Lock()
		for _, conn := range wt.clients {
			conn.Close()
		}
		wt.clients = make(map[string]*websocket.Conn)
		wt.clientsMu.Unlock()
		
		// 关闭服务器
		if wt.server != nil {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			wt.server.Shutdown(ctx)
		}
	} else {
		// 关闭客户端连接
		if wt.conn != nil {
			wt.conn.Close()
		}
	}
	
	wt.isConnected = false
	
	slog.Info("WebSocket传输层已关闭", "node_id", wt.config.NodeID)
	return nil
}

// IsConnected 检查连接状态
func (wt *WebSocketTransport) IsConnected() bool {
	wt.mu.RLock()
	defer wt.mu.RUnlock()
	
	return wt.isConnected
}
