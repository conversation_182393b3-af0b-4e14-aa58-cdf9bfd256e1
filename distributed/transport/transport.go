package transport

import (
	"context"
	"fmt"

	"github.com/beck-8/subs-check/distributed"
)

// Transport 传输层接口
type Transport interface {
	// Connect 连接到远程服务器 (用于客户端)
	Connect(ctx context.Context) error
	
	// Listen 启动服务器监听 (用于服务器端)
	Listen(ctx context.Context) error
	
	// Send 发送消息
	Send(msg *distributed.Message) error
	
	// Receive 接收消息 (阻塞)
	Receive(ctx context.Context) (*distributed.Message, error)
	
	// Close 关闭连接
	Close() error
	
	// IsConnected 检查连接状态
	IsConnected() bool
}

// TransportType 传输类型
type TransportType string

const (
	TransportTypeWebSocket TransportType = "websocket"
	TransportTypeNATS      TransportType = "nats"
)

// NewTransport 创建传输层实例
func NewTransport(config *distributed.DistributedConfig) (Transport, error) {
	// 优先使用NATS，如果配置了NATS服务器
	if len(config.NATSServers) > 0 {
		return NewNATSTransport(config)
	}
	
	// 否则使用WebSocket
	if config.WSEndpoint != "" || config.CollectorURL != "" {
		return NewWebSocketTransport(config)
	}
	
	return nil, fmt.Errorf("没有配置有效的传输方式")
}
