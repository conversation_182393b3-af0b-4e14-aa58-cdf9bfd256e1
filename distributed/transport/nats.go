package transport

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/beck-8/subs-check/distributed"
	"github.com/nats-io/nats.go"
	"github.com/nats-io/nats.go/jetstream"
)

// NATSTransport NATS传输实现
type NATSTransport struct {
	config     *distributed.DistributedConfig
	conn       *nats.Conn
	js         jetstream.JetStream
	consumer   jetstream.Consumer
	stream     jetstream.Stream
	ctx        context.Context
	cancel     context.CancelFunc
	mu         sync.RWMutex
	isConnected bool
	
	// 消息通道
	incomingMsgs chan *distributed.Message
	
	// 流和消费者配置
	streamName   string
	consumerName string
}

// NewNATSTransport 创建NATS传输实例
func NewNATSTransport(config *distributed.DistributedConfig) (*NATSTransport, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	transport := &NATSTransport{
		config:       config,
		ctx:          ctx,
		cancel:       cancel,
		incomingMsgs: make(chan *distributed.Message, 1000),
		streamName:   "SUBS_CHECK_DISTRIBUTED",
		consumerName: fmt.Sprintf("consumer_%s", config.NodeID),
	}
	
	return transport, nil
}

// Connect 连接到NATS服务器
func (nt *NATSTransport) Connect(ctx context.Context) error {
	nt.mu.Lock()
	defer nt.mu.Unlock()
	
	if nt.isConnected {
		return nil
	}
	
	// 连接选项
	opts := []nats.Option{
		nats.Name(fmt.Sprintf("subs-check-%s", nt.config.NodeID)),
		nats.RetryOnFailedConnect(true),
		nats.MaxReconnects(10),
		nats.ReconnectWait(time.Second * 2),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			slog.Info("NATS重连成功", "node_id", nt.config.NodeID)
		}),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			slog.Warn("NATS连接断开", "node_id", nt.config.NodeID, "error", err)
		}),
		nats.ClosedHandler(func(nc *nats.Conn) {
			slog.Info("NATS连接关闭", "node_id", nt.config.NodeID)
		}),
	}
	
	// 如果配置了认证令牌
	if nt.config.AuthToken != "" {
		opts = append(opts, nats.Token(nt.config.AuthToken))
	}
	
	// 如果配置了TLS
	if nt.config.EnableTLS {
		if nt.config.CertFile != "" && nt.config.KeyFile != "" {
			opts = append(opts, nats.ClientCert(nt.config.CertFile, nt.config.KeyFile))
		} else {
			opts = append(opts, nats.Secure())
		}
	}
	
	// 连接到NATS服务器
	var err error
	if len(nt.config.NATSServers) > 0 {
		nt.conn, err = nats.Connect(nats.DefaultURL, opts...)
		// 如果有多个服务器，使用第一个作为主服务器
		if len(nt.config.NATSServers) > 1 {
			nt.conn.Close()
			serverURLs := make([]string, len(nt.config.NATSServers))
			copy(serverURLs, nt.config.NATSServers)
			nt.conn, err = nats.Connect(serverURLs[0], opts...)
		}
	} else {
		nt.conn, err = nats.Connect(nats.DefaultURL, opts...)
	}
	
	if err != nil {
		return fmt.Errorf("连接NATS服务器失败: %w", err)
	}
	
	// 创建JetStream上下文
	nt.js, err = jetstream.New(nt.conn)
	if err != nil {
		nt.conn.Close()
		return fmt.Errorf("创建JetStream上下文失败: %w", err)
	}
	
	// 确保流存在
	if err := nt.ensureStream(ctx); err != nil {
		nt.conn.Close()
		return fmt.Errorf("确保流存在失败: %w", err)
	}
	
	// 创建消费者
	if err := nt.createConsumer(ctx); err != nil {
		nt.conn.Close()
		return fmt.Errorf("创建消费者失败: %w", err)
	}
	
	nt.isConnected = true
	
	// 启动消息接收器
	go nt.messageReceiver()
	
	slog.Info("NATS传输层连接成功", "node_id", nt.config.NodeID, "stream", nt.streamName)
	return nil
}

// Listen 启动服务器监听 (对于NATS，这与Connect相同)
func (nt *NATSTransport) Listen(ctx context.Context) error {
	return nt.Connect(ctx)
}

// ensureStream 确保流存在
func (nt *NATSTransport) ensureStream(ctx context.Context) error {
	// 尝试获取现有流
	stream, err := nt.js.Stream(ctx, nt.streamName)
	if err == nil {
		nt.stream = stream
		return nil
	}
	
	// 如果流不存在，创建新流
	streamConfig := jetstream.StreamConfig{
		Name:        nt.streamName,
		Description: "分布式节点检测系统消息流",
		Subjects:    []string{"subs-check.>"},
		Retention:   jetstream.WorkQueuePolicy,
		MaxAge:      24 * time.Hour, // 消息保留24小时
		MaxBytes:    100 * 1024 * 1024, // 最大100MB
		MaxMsgs:     100000, // 最大消息数
		Replicas:    1,
	}
	
	nt.stream, err = nt.js.CreateStream(ctx, streamConfig)
	if err != nil {
		return fmt.Errorf("创建流失败: %w", err)
	}
	
	slog.Info("创建NATS流成功", "stream", nt.streamName)
	return nil
}

// createConsumer 创建消费者
func (nt *NATSTransport) createConsumer(ctx context.Context) error {
	// 根据节点类型确定消费者配置
	var filterSubject string
	if nt.config.NodeType == distributed.NodeTypeAgent {
		// 检测节点只接收发给自己的消息和广播消息
		filterSubject = fmt.Sprintf("subs-check.agent.%s", nt.config.NodeID)
	} else {
		// 汇总服务器接收所有消息
		filterSubject = "subs-check.>"
	}
	
	consumerConfig := jetstream.ConsumerConfig{
		Name:          nt.consumerName,
		Description:   fmt.Sprintf("节点 %s 的消费者", nt.config.NodeID),
		FilterSubject: filterSubject,
		AckPolicy:     jetstream.AckExplicitPolicy,
		DeliverPolicy: jetstream.DeliverNewPolicy,
		MaxDeliver:    3,
		AckWait:       30 * time.Second,
	}
	
	var err error
	nt.consumer, err = nt.stream.CreateOrUpdateConsumer(ctx, consumerConfig)
	if err != nil {
		return fmt.Errorf("创建消费者失败: %w", err)
	}
	
	slog.Info("创建NATS消费者成功", "consumer", nt.consumerName, "filter", filterSubject)
	return nil
}

// Send 发送消息
func (nt *NATSTransport) Send(msg *distributed.Message) error {
	nt.mu.RLock()
	defer nt.mu.RUnlock()
	
	if !nt.isConnected {
		return fmt.Errorf("NATS未连接")
	}
	
	// 序列化消息
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}
	
	// 确定目标主题
	subject := nt.getSubjectForMessage(msg)
	
	// 发布消息
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	_, err = nt.js.Publish(ctx, subject, data)
	if err != nil {
		return fmt.Errorf("发布消息失败: %w", err)
	}
	
	slog.Debug("发送NATS消息", "subject", subject, "msg_type", msg.Type, "to", msg.To)
	return nil
}

// getSubjectForMessage 根据消息确定目标主题
func (nt *NATSTransport) getSubjectForMessage(msg *distributed.Message) string {
	if msg.To == "" {
		// 广播消息
		return "subs-check.broadcast"
	}
	
	// 点对点消息
	return fmt.Sprintf("subs-check.agent.%s", msg.To)
}

// messageReceiver 消息接收器
func (nt *NATSTransport) messageReceiver() {
	defer close(nt.incomingMsgs)
	
	// 创建消息迭代器
	iter, err := nt.consumer.Messages(
		jetstream.PullMaxMessages(10),
		jetstream.PullExpiry(30*time.Second),
		jetstream.PullHeartbeat(5*time.Second),
	)
	if err != nil {
		slog.Error("创建消息迭代器失败", "error", err)
		return
	}
	defer iter.Stop()
	
	for {
		select {
		case <-nt.ctx.Done():
			return
		default:
			// 获取下一条消息
			natsMsg, err := iter.Next()
			if err != nil {
				if nt.ctx.Err() != nil {
					return
				}
				slog.Error("接收NATS消息失败", "error", err)
				continue
			}
			
			// 反序列化消息
			var msg distributed.Message
			if err := json.Unmarshal(natsMsg.Data(), &msg); err != nil {
				slog.Error("反序列化消息失败", "error", err)
				natsMsg.Nak() // 拒绝消息
				continue
			}
			
			// 检查消息是否是发给当前节点的
			if !nt.isMessageForMe(&msg) {
				natsMsg.Ack() // 确认消息但不处理
				continue
			}
			
			// 发送到内部通道
			select {
			case nt.incomingMsgs <- &msg:
				natsMsg.Ack() // 确认消息
				slog.Debug("接收NATS消息", "msg_type", msg.Type, "from", msg.From)
			case <-nt.ctx.Done():
				natsMsg.Nak() // 拒绝消息
				return
			default:
				slog.Warn("消息通道已满，丢弃消息", "msg_type", msg.Type)
				natsMsg.Nak() // 拒绝消息
			}
		}
	}
}

// isMessageForMe 检查消息是否是发给当前节点的
func (nt *NATSTransport) isMessageForMe(msg *distributed.Message) bool {
	// 广播消息
	if msg.To == "" {
		return true
	}
	
	// 点对点消息
	return msg.To == nt.config.NodeID
}

// Receive 接收消息
func (nt *NATSTransport) Receive(ctx context.Context) (*distributed.Message, error) {
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case msg, ok := <-nt.incomingMsgs:
		if !ok {
			return nil, fmt.Errorf("消息通道已关闭")
		}
		return msg, nil
	}
}

// Close 关闭连接
func (nt *NATSTransport) Close() error {
	nt.mu.Lock()
	defer nt.mu.Unlock()
	
	if !nt.isConnected {
		return nil
	}
	
	nt.cancel()
	
	if nt.consumer != nil {
		// 删除消费者 (可选)
		// nt.js.DeleteConsumer(context.Background(), nt.streamName, nt.consumerName)
	}
	
	if nt.conn != nil {
		nt.conn.Close()
	}
	
	nt.isConnected = false
	
	slog.Info("NATS传输层已关闭", "node_id", nt.config.NodeID)
	return nil
}

// IsConnected 检查连接状态
func (nt *NATSTransport) IsConnected() bool {
	nt.mu.RLock()
	defer nt.mu.RUnlock()
	
	return nt.isConnected && nt.conn != nil && nt.conn.IsConnected()
}
