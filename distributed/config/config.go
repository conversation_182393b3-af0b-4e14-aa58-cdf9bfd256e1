package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/beck-8/subs-check/distributed"
	"gopkg.in/yaml.v3"
)

// LoadDistributedConfig 加载分布式配置
func LoadDistributedConfig(configPath string) (*distributed.DistributedConfig, error) {
	// 如果没有指定配置文件，使用默认路径
	if configPath == "" {
		configPath = "distributed.yaml"
	}
	
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 如果配置文件不存在，创建默认配置
		return createDefaultConfig(configPath)
	}
	
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	// 解析配置
	var config distributed.DistributedConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}
	
	// 验证和补充配置
	if err := validateAndCompleteConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}
	
	return &config, nil
}

// SaveDistributedConfig 保存分布式配置
func SaveDistributedConfig(config *distributed.DistributedConfig, configPath string) error {
	if configPath == "" {
		configPath = "distributed.yaml"
	}
	
	// 确保目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}
	
	// 序列化配置
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}
	
	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}
	
	return nil
}

// createDefaultConfig 创建默认配置
func createDefaultConfig(configPath string) (*distributed.DistributedConfig, error) {
	// 检测当前环境，生成合适的默认配置
	nodeType := distributed.NodeTypeAgent
	if isCollectorEnvironment() {
		nodeType = distributed.NodeTypeCollector
	}
	
	config := &distributed.DistributedConfig{
		// 节点配置
		NodeID:   generateNodeID(),
		NodeType: nodeType,
		Region:   detectRegion(),
		Location: detectLocation(),
		
		// 通信配置
		CollectorURL: getDefaultCollectorURL(),
		NATSServers:  getDefaultNATSServers(),
		WSEndpoint:   getDefaultWSEndpoint(),
		
		// 检测配置
		CheckInterval:    5 * time.Minute,
		TaskTimeout:      2 * time.Minute,
		MaxConcurrent:    10,
		RetryAttempts:    3,
		
		// 健康检查配置
		HeartbeatInterval:    30 * time.Second,
		HealthCheckTimeout:   60 * time.Second,
		
		// 数据聚合配置
		AggregationWindow:     2 * time.Minute,
		MinRegions:           2,
		ConfidenceThreshold:  0.7,
		
		// 安全配置
		EnableTLS:    false,
		CertFile:     "",
		KeyFile:      "",
		AuthToken:    "",
	}
	
	// 保存默认配置
	if err := SaveDistributedConfig(config, configPath); err != nil {
		return nil, fmt.Errorf("保存默认配置失败: %w", err)
	}
	
	return config, nil
}

// validateAndCompleteConfig 验证和补充配置
func validateAndCompleteConfig(config *distributed.DistributedConfig) error {
	// 验证必需字段
	if config.NodeID == "" {
		config.NodeID = generateNodeID()
	}
	
	if config.NodeType == "" {
		config.NodeType = distributed.NodeTypeAgent
	}
	
	if config.Region == "" {
		config.Region = detectRegion()
	}
	
	if config.Location == "" {
		config.Location = detectLocation()
	}
	
	// 验证节点类型
	if config.NodeType != distributed.NodeTypeAgent && config.NodeType != distributed.NodeTypeCollector {
		return fmt.Errorf("无效的节点类型: %s", config.NodeType)
	}
	
	// 验证通信配置
	if len(config.NATSServers) == 0 && config.CollectorURL == "" && config.WSEndpoint == "" {
		return fmt.Errorf("必须配置至少一种通信方式 (NATS服务器、汇总服务器URL或WebSocket端点)")
	}
	
	// 设置默认值
	if config.CheckInterval == 0 {
		config.CheckInterval = 5 * time.Minute
	}
	
	if config.TaskTimeout == 0 {
		config.TaskTimeout = 2 * time.Minute
	}
	
	if config.MaxConcurrent == 0 {
		config.MaxConcurrent = 10
	}
	
	if config.RetryAttempts == 0 {
		config.RetryAttempts = 3
	}
	
	if config.HeartbeatInterval == 0 {
		config.HeartbeatInterval = 30 * time.Second
	}
	
	if config.HealthCheckTimeout == 0 {
		config.HealthCheckTimeout = 60 * time.Second
	}
	
	if config.AggregationWindow == 0 {
		config.AggregationWindow = 2 * time.Minute
	}
	
	if config.MinRegions == 0 {
		config.MinRegions = 2
	}
	
	if config.ConfidenceThreshold == 0 {
		config.ConfidenceThreshold = 0.7
	}
	
	return nil
}

// isCollectorEnvironment 检测是否为汇总服务器环境
func isCollectorEnvironment() bool {
	// 检查环境变量
	if os.Getenv("SUBS_CHECK_ROLE") == "collector" {
		return true
	}
	
	// 检查是否有公网IP或域名配置
	if os.Getenv("SUBS_CHECK_PUBLIC_URL") != "" {
		return true
	}
	
	// 检查是否在云服务器环境
	if isCloudEnvironment() {
		return true
	}
	
	return false
}

// isCloudEnvironment 检测是否在云服务器环境
func isCloudEnvironment() bool {
	// 检查常见的云服务器标识
	cloudIndicators := []string{
		"/sys/hypervisor/uuid",
		"/sys/class/dmi/id/product_uuid",
		"/proc/xen",
	}
	
	for _, indicator := range cloudIndicators {
		if _, err := os.Stat(indicator); err == nil {
			return true
		}
	}
	
	// 检查环境变量
	cloudEnvVars := []string{
		"AWS_REGION",
		"GOOGLE_CLOUD_PROJECT",
		"AZURE_RESOURCE_GROUP",
		"ALIBABA_CLOUD_REGION",
	}
	
	for _, envVar := range cloudEnvVars {
		if os.Getenv(envVar) != "" {
			return true
		}
	}
	
	return false
}

// generateNodeID 生成节点ID
func generateNodeID() string {
	// 使用主机名 + 时间戳生成唯一ID
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}
	
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s-%d", hostname, timestamp)
}

// detectRegion 检测地理区域
func detectRegion() string {
	// 优先使用环境变量
	if region := os.Getenv("SUBS_CHECK_REGION"); region != "" {
		return region
	}
	
	// 尝试从云服务商元数据获取
	if region := detectCloudRegion(); region != "" {
		return region
	}
	
	// 默认使用中国
	return "CN"
}

// detectCloudRegion 从云服务商元数据检测区域
func detectCloudRegion() string {
	// AWS
	if region := os.Getenv("AWS_REGION"); region != "" {
		return region
	}
	
	// Google Cloud
	if region := os.Getenv("GOOGLE_CLOUD_REGION"); region != "" {
		return region
	}
	
	// Azure
	if region := os.Getenv("AZURE_REGION"); region != "" {
		return region
	}
	
	// 阿里云
	if region := os.Getenv("ALIBABA_CLOUD_REGION"); region != "" {
		return region
	}
	
	return ""
}

// detectLocation 检测具体位置
func detectLocation() string {
	// 优先使用环境变量
	if location := os.Getenv("SUBS_CHECK_LOCATION"); location != "" {
		return location
	}
	
	// 根据区域推断位置
	region := detectRegion()
	switch region {
	case "CN":
		return "China"
	case "US":
		return "United States"
	case "EU":
		return "Europe"
	case "JP":
		return "Japan"
	case "SG":
		return "Singapore"
	default:
		return "Unknown"
	}
}

// getDefaultCollectorURL 获取默认汇总服务器URL
func getDefaultCollectorURL() string {
	if url := os.Getenv("SUBS_CHECK_COLLECTOR_URL"); url != "" {
		return url
	}
	
	// 如果是汇总服务器，返回空
	if isCollectorEnvironment() {
		return ""
	}
	
	// 默认使用本地测试地址
	return "ws://localhost:8080/ws"
}

// getDefaultNATSServers 获取默认NATS服务器列表
func getDefaultNATSServers() []string {
	if servers := os.Getenv("SUBS_CHECK_NATS_SERVERS"); servers != "" {
		// 简单的逗号分隔解析
		return []string{servers}
	}
	
	// 如果配置了NATS，返回默认服务器
	if os.Getenv("SUBS_CHECK_USE_NATS") == "true" {
		return []string{"nats://localhost:4222"}
	}
	
	return []string{}
}

// getDefaultWSEndpoint 获取默认WebSocket端点
func getDefaultWSEndpoint() string {
	if endpoint := os.Getenv("SUBS_CHECK_WS_ENDPOINT"); endpoint != "" {
		return endpoint
	}
	
	// 如果是汇总服务器，返回监听地址
	if isCollectorEnvironment() {
		return ":8080"
	}
	
	return ""
}

// GetConfigTemplate 获取配置模板
func GetConfigTemplate(nodeType distributed.NodeType) string {
	template := `# 分布式节点检测配置文件
# 节点配置
node_id: "%s"           # 节点唯一标识
node_type: "%s"         # 节点类型: agent(检测节点) 或 collector(汇总服务器)
region: "%s"            # 地理区域: CN, US, EU, JP, SG 等
location: "%s"          # 具体位置描述

# 通信配置
collector_url: "%s"     # 汇总服务器地址 (检测节点使用)
nats_servers:           # NATS服务器列表 (可选，优先级高于WebSocket)
%s
ws_endpoint: "%s"       # WebSocket监听端点 (汇总服务器使用)

# 检测配置
check_interval: %s      # 检测间隔
task_timeout: %s        # 任务超时时间
max_concurrent: %d      # 最大并发数
retry_attempts: %d      # 重试次数

# 健康检查配置
heartbeat_interval: %s  # 心跳间隔
health_check_timeout: %s # 健康检查超时

# 数据聚合配置 (仅汇总服务器使用)
aggregation_window: %s  # 聚合时间窗口
min_regions: %d         # 最少区域数
confidence_threshold: %.1f # 置信度阈值

# 安全配置
enable_tls: %t          # 启用TLS加密
cert_file: "%s"         # TLS证书文件路径
key_file: "%s"          # TLS密钥文件路径
auth_token: "%s"        # 认证令牌
`
	
	config := &distributed.DistributedConfig{}
	validateAndCompleteConfig(config)
	config.NodeType = nodeType
	
	natsServers := ""
	if len(config.NATSServers) > 0 {
		natsServers = fmt.Sprintf("  - %s", config.NATSServers[0])
	} else {
		natsServers = "  # - nats://localhost:4222"
	}
	
	return fmt.Sprintf(template,
		config.NodeID,
		config.NodeType,
		config.Region,
		config.Location,
		config.CollectorURL,
		natsServers,
		config.WSEndpoint,
		config.CheckInterval,
		config.TaskTimeout,
		config.MaxConcurrent,
		config.RetryAttempts,
		config.HeartbeatInterval,
		config.HealthCheckTimeout,
		config.AggregationWindow,
		config.MinRegions,
		config.ConfidenceThreshold,
		config.EnableTLS,
		config.CertFile,
		config.KeyFile,
		config.AuthToken,
	)
}
