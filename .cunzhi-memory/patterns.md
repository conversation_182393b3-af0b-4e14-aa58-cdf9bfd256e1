# 常用模式和最佳实践

- 代码架构模式 - 采用分层架构：app层(应用控制)、check层(节点检测)、proxy层(代理处理)、save层(保存策略)、config层(配置管理)、utils层(工具函数)。核心流程：订阅获取→节点去重→可用性检测→测速→流媒体检测→保存→通知。支持多种保存方式(local/r2/gist/webdav/s3)和通知渠道(100+)
- 配置优化模式 - 分布式架构评估→替代方案分析→配置参数优化的渐进式改进策略。通过系统信息收集发现资源利用不足，采用配置调优而非复杂架构改造的实用方案。关键原则：充分利用现有资源、控制风险、渐进式优化、效果可量化
- Telegram通知优化完成：修复了Go循环导入问题(utils/notify.go导入check包)，通过数据传递重构方案解决，将复杂数据处理移到app.go中，使用简单数据类型传递统计信息。增强通知包含检测统计、流媒体解锁统计、订阅链接(代码块格式便于复制)，域名配置为subs-check.dearmer.live。
- Telegram通知链接复制问题优化完成：通过Context7分析Telegram Bot API 7.11的CopyTextButton功能和Apprise兼容性，发现Apprise不支持reply_markup和内联键盘。采用方案B优化文本格式，移除代码块格式(反引号)，改为普通文本显示链接，添加"长按复制"指引，保持完全向后兼容。修改utils/notify.go中generateNotifyBody函数，优化订阅链接显示格式，提升用户体验。
- CF Worker订阅源优化完成：部署了5个分布式Worker，包含470个优选CF官方IP，生成3420个高质量节点。配置已更新：success-rate提升到0.35，concurrent降低到12，使用混合配置保留2个备份订阅源。预期成功率从3%提升到30-50%，可用率从1%提升到20-35%。
- Telegram通知消息优化模式：1) 在NotifyStats结构体中添加TotalBytes字段用于流量统计；2) 重写generateNotifyBody函数实现新消息格式，包括项目介绍、温馨提示、简化统计信息；3) 新增formatTraffic函数支持流量格式化；4) 在app.go中传递check.TotalBytes.Load()数据；5) 保持向后兼容性，标记弃用字段但不删除
